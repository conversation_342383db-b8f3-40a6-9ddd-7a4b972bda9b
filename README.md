# RustyCluster

RustyCluster is a distributed key-value store built in Rust, providing high availability and data consistency through replication. It uses Redis as the underlying storage engine and gRPC for inter-node communication.

## Features

- **Distributed Architecture**: Multiple nodes working together to provide high availability
- **Data Replication**: Automatic replication of data to secondary nodes with configurable consistency levels
- **Site Replication**: Cross-site data replication with automatic failover for disaster recovery
- **Write Consistency**: Synchronous replication to peer Redis nodes with configurable consistency levels (ALL/QUORUM/ONE)
- **Connection Pooling**: Efficient connection management for Redis, inter-node communication, and peer Redis pools
- **Asynchronous Operations**: Support for both synchronous and asynchronous replication modes
- **Rich Data Types**: Comprehensive support for strings, hashes, numeric operations, and Lua scripts
- **TTL Support**: Built-in support for key expiration and time-based operations
- **Client Authentication**: Secure username/password authentication with session management
- **Redis Authentication**: Support for Redis 6.0+ ACL and legacy password authentication
- **Rate Limiting**: Per-client rate limiting with token bucket algorithm to prevent abuse and ensure fair resource usage
- **Performance Monitoring**: Integration with Tokio Console for real-time profiling and debugging
- **Structured Logging**: Advanced logging with tracing crate for better observability
- **Batch Operations**: Efficient batch processing for high-throughput scenarios
- **Health Monitoring**: Comprehensive health checks for Redis, secondary nodes, and site replication

## Architecture

RustyCluster consists of multiple nodes, where:
- Each node can be either a primary or secondary node
- Primary nodes handle client requests and replicate data to secondary nodes
- Secondary nodes maintain copies of the data and can serve read requests
- All nodes communicate via gRPC
- Redis is used as the underlying storage engine

## Prerequisites

- Rust (latest stable version)
- Redis server
- Protobuf compiler (for gRPC)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/rustycluster.git
cd rustycluster
```

2. Build the project:
```bash
cargo build --release
```

## Configuration

RustyCluster uses TOML configuration files. A sample configuration file (`config.toml`) looks like this:

```toml
# Redis connection URL for the primary node
# For Redis without authentication:
redis_url = "redis://127.0.0.1:6379"
# For Redis with authentication (replace 'your_password' with your actual password):
# redis_url = "redis://:your_password@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 2

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Write consistency configuration for peer Redis nodes
write_consistency = "QUORUM"  # Options: ALL, QUORUM, ONE
peer_redis_nodes = ["redis://127.0.0.1:6380", "redis://127.0.0.1:6381"]
peer_redis_pool_size = 64

# Site replication configuration
site_replication_enabled = false
site_replication_nodes = [
    { primary = "http://site2-primary:50053", failover = "http://site2-failover:50054" },
    { primary = "http://site3-primary:50053", failover = "http://site3-failover:50054" }
]

# Health check configuration
health_check_enabled = true
health_check_interval_secs = 30
redis_health_check_timeout_secs = 5
secondary_nodes_health_check_timeout_secs = 10

# Performance tuning
use_physical_connections = true  # Use actual TCP connections for better monitoring
batch_flush_interval_ms = 100
max_concurrent_operations = 1000
```

## Client Authentication

RustyCluster supports client authentication with username and password. When enabled, clients must authenticate once during connection initialization before performing any operations.

### Authentication Configuration

Add the following settings to your `config.toml` file:

```toml
# Authentication configuration
auth_enabled = true
auth_username = "your_username"
auth_password = "your_password"
session_duration_secs = 3600  # Session timeout in seconds (1 hour)
```

### Authentication Flow

1. **Connection Initialization**: Client connects to RustyCluster
2. **Authentication**: Client calls the `Authenticate` RPC with username and password
3. **Session Token**: Server returns a session token upon successful authentication
4. **Operations**: Client includes the session token in the `authorization` header for all subsequent operations

### Authentication Notes

- Authentication happens only once during connection initialization
- No authentication is required on each individual operation after the initial authentication
- Session tokens expire after the configured duration (default: 1 hour)
- Authentication is not required between RustyCluster nodes (inter-node communication)
- When `auth_enabled = false`, all requests are allowed without authentication

### Example Usage

1. **Start RustyCluster with authentication enabled**:
```bash
cargo run --release config_auth_test.toml
```

2. **Client Authentication Flow**:
```protobuf
// First, authenticate to get a session token
AuthenticateRequest {
  username: "testuser"
  password: "testpass"
}

// Server responds with:
AuthenticateResponse {
  success: true
  session_token: "uuid-session-token"
  message: "Authentication successful"
}

// Use the session token in subsequent requests
// Add to gRPC metadata: authorization: "Bearer uuid-session-token"
```

3. **All subsequent operations** (Set, Get, Delete, etc.) must include the session token in the authorization header.

### Load Testing with Authentication

For load testing with tools like `ghz`, you have several options:

#### Option 1: Disable Authentication (Recommended for Load Testing)
```bash
# Use config_loadtest.toml with auth_enabled = false
cargo run --release config_loadtest.toml

# Run your existing ghz command
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

#### Option 2: Use ghz with Session Token
```bash
# Get session token
python get_session_token.py --username testuser --password testpass

# Use token with ghz (replace YOUR_SESSION_TOKEN)
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --metadata="authorization:Bearer YOUR_SESSION_TOKEN" --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

#### Option 3: Automated Script
```bash
# Make script executable
chmod +x ghz_with_auth.sh

# Run with authentication
./ghz_with_auth.sh --username testuser --password testpass --rps 10000 -n 200000 -c 1000

# Run without authentication
./ghz_with_auth.sh --no-auth --rps 10000 -n 200000 -c 1000
```

#### Option 4: Windows Batch Script
```cmd
# Get session token (Windows)
get_token.bat --username testuser --password testpass

# Use the displayed token with ghz
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --metadata="authorization:Bearer YOUR_TOKEN" --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

📖 **For detailed load testing instructions with authentication, see [LOAD_TEST_WITH_AUTH.md](LOAD_TEST_WITH_AUTH.md)**

### Java Client Example

```java
// Java client authentication example
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;

// Create channel
ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 50051)
    .usePlaintext()
    .build();

KeyValueServiceGrpc.KeyValueServiceBlockingStub stub =
    KeyValueServiceGrpc.newBlockingStub(channel);

// Authenticate
AuthenticateRequest authRequest = AuthenticateRequest.newBuilder()
    .setUsername("testuser")
    .setPassword("testpass")
    .build();

AuthenticateResponse authResponse = stub.authenticate(authRequest);

if (authResponse.getSuccess()) {
    String sessionToken = authResponse.getSessionToken();

    // Create metadata with session token
    Metadata metadata = new Metadata();
    Metadata.Key<String> authKey = Metadata.Key.of("authorization",
        Metadata.ASCII_STRING_MARSHALLER);
    metadata.put(authKey, "Bearer " + sessionToken);

    // Use authenticated stub for operations
    KeyValueServiceGrpc.KeyValueServiceBlockingStub authenticatedStub =
        MetadataUtils.attachHeaders(stub, metadata);

    // Now you can use authenticatedStub for all operations
    PingResponse pingResponse = authenticatedStub.ping(PingRequest.newBuilder().build());
}
```

## Redis Authentication

RustyCluster supports Redis servers with various authentication methods. Configure your Redis connection in the `config.toml` file using one of the following formats:

### Authentication Options

1. **Username and Password Authentication** (recommended for Redis 6.0+):
```toml
# For Redis with both username and password
redis_url = "redis://username:password@127.0.0.1:6379"
```

2. **Password-only Authentication**:
```toml
# For Redis with password only (no username)
redis_url = "redis://:password@127.0.0.1:6379"
```

3. **Username-only Authentication**:
```toml
# For Redis with username only (treated as password)
redis_url = "redis://username@127.0.0.1:6379"
```

4. **No Authentication**:
```toml
# For Redis without authentication
redis_url = "redis://127.0.0.1:6379"
```

### Authentication Notes

- Redis 6.0+ supports ACL with username and password authentication
- For Redis versions before 6.0, use the password-only format
- When using username-only format (`redis://username@host:port`), Redis treats the username as a password
- For security, RustyCluster logs Redis URLs without exposing credentials

## Password Encryption

RustyCluster supports encrypted password storage for enhanced security. This feature allows you to store sensitive credentials (authentication passwords and Redis passwords) in encrypted format within configuration files.

### Encryption Overview

The encryption system uses:
- **AES-256-GCM** encryption for maximum security
- **PBKDF2** key derivation with 100,000 iterations
- **Base64 encoding** for storage in configuration files
- **Automatic detection** of encrypted vs plain text passwords
- **Backward compatibility** with existing plain text configurations

### Quick Start

#### 1. Set Master Key (Recommended)

Set the master encryption key as an environment variable:

```bash
# Windows (PowerShell)
$env:RUSTYCLUSTER_MASTER_KEY="your_secure_master_key_here"

# Windows (Command Prompt)
set RUSTYCLUSTER_MASTER_KEY=your_secure_master_key_here

# Linux/macOS
export RUSTYCLUSTER_MASTER_KEY="your_secure_master_key_here"
```

**Important:** If you don't set this variable, a default key will be used, which is less secure.

#### 2. Encrypt Your Passwords

Use the built-in encryption utility:

```bash
cargo run --bin encrypt_password
```

Example session:
```
RustyCluster Password Encryption Utility
========================================

✓ Using RUSTYCLUSTER_MASTER_KEY environment variable

Enter password to encrypt (or 'quit' to exit): mypassword123

✓ Password encrypted successfully!
Original:  mypassword123
Encrypted: ENC:SGVsbG9Xb3JsZA==

Copy the encrypted value to your configuration file.
```

#### 3. Update Configuration Files

Replace plain text passwords with encrypted ones:

```toml
# Before (plain text)
auth_password = "mypassword123"
redis_url = "redis://user:mypassword123@localhost:6379"

# After (encrypted)
auth_password = "ENC:SGVsbG9Xb3JsZA=="
redis_url = "redis://user:ENC:SGVsbG9Xb3JsZA==@localhost:6379"
```

#### 4. Run RustyCluster

The application automatically detects and decrypts encrypted passwords:

```bash
cargo run --release config.toml
```

### Supported Password Fields

The following configuration fields support encryption:

#### Authentication Passwords
- `auth_password` - Client authentication password

#### Redis Connection URLs
- `redis_url` - Primary Redis connection URL
- `peer_redis_nodes` - List of peer Redis URLs for write consistency

#### Redis URL Formats

All Redis URL formats are supported:

```toml
# Username and password
redis_url = "redis://user:ENC:encrypted_password@host:port"

# Password only
redis_url = "redis://:ENC:encrypted_password@host:port"

# Username only (treated as password by Redis)
redis_url = "redis://ENC:encrypted_username@host:port"
```

### Utility Commands

#### Encrypt Passwords

```bash
cargo run --bin encrypt_password
```

Interactive utility to encrypt passwords for configuration files.

#### Decrypt Passwords (Verification)

```bash
cargo run --bin decrypt_password
```

Interactive utility to decrypt and verify encrypted passwords.

### Security Best Practices

#### 1. Master Key Management

- **Use unique master keys** for different environments (dev, staging, production)
- **Never commit master keys** to version control
- **Store master keys securely** using:
  - Environment variables
  - Secure key management systems (AWS KMS, Azure Key Vault, etc.)
  - Configuration management tools (Ansible Vault, etc.)

#### 2. Key Rotation

To rotate your master key:

1. Generate a new master key
2. Re-encrypt all passwords with the new key
3. Update configuration files
4. Deploy with the new master key

#### 3. Environment Separation

Use different master keys for each environment:

```bash
# Development
export RUSTYCLUSTER_MASTER_KEY="dev_key_12345"

# Staging
export RUSTYCLUSTER_MASTER_KEY="staging_key_67890"

# Production
export RUSTYCLUSTER_MASTER_KEY="prod_key_abcdef"
```

### Backward Compatibility

The encryption system is fully backward compatible:

- **Plain text passwords** continue to work without changes
- **Mixed configurations** (some encrypted, some plain text) are supported
- **No breaking changes** to existing deployments

### Troubleshooting

#### Common Issues

1. **"Failed to decrypt passwords" error**
   - Ensure `RUSTYCLUSTER_MASTER_KEY` is set correctly
   - Verify you're using the same master key used for encryption

2. **"Invalid format" error**
   - Check that encrypted passwords start with `ENC:`
   - Verify the base64 encoding is not corrupted

3. **Connection failures after encryption**
   - Test decryption using `cargo run --bin decrypt_password`
   - Verify the original password was encrypted correctly

#### Debug Mode

Enable debug logging to see encryption/decryption activity:

```bash
RUST_LOG=debug cargo run --release config.toml
```

### Example Configuration

See `config_encrypted_example.toml` for a complete example with encrypted passwords.

### Migration Guide

#### From Plain Text to Encrypted

1. **Backup** your current configuration files
2. **Set** the master key environment variable
3. **Encrypt** each password using the utility
4. **Update** configuration files with encrypted values
5. **Test** the application startup
6. **Verify** all connections work correctly

#### Bulk Migration Script

For multiple passwords, you can create a simple script:

```bash
#!/bin/bash
export RUSTYCLUSTER_MASTER_KEY="your_master_key"

echo "password1" | cargo run --bin encrypt_password
echo "password2" | cargo run --bin encrypt_password
echo "password3" | cargo run --bin encrypt_password
```

## Hot Configuration Reload

RustyCluster supports hot configuration reload for certain configuration parameters, allowing you to update settings at runtime without restarting the server.

### Supported Hot-Reloadable Settings

#### Rate Limiting Configuration
All rate limiting settings can be updated at runtime:
- `rate_limiting_enabled` - Enable/disable rate limiting
- `rate_limit_type` - Change between "global", "per_client", or "both"
- `rate_limit_requests_per_second` - Per-client rate limit
- `rate_limit_burst_size` - Per-client burst size
- `rate_limit_global_requests_per_second` - Global rate limit
- `rate_limit_global_burst_size` - Global burst size
- `rate_limit_cleanup_interval_secs` - Cleanup interval

#### Health Check Configuration
Health check settings can be updated at runtime:
- `redis_keepalive_enabled` - Enable/disable Redis health checks
- `redis_keepalive_interval_secs` - Redis health check interval
- `redis_idle_threshold_secs` - Redis idle threshold
- `secondary_nodes_keepalive_enabled` - Enable/disable secondary node health checks
- `secondary_nodes_keepalive_interval_secs` - Secondary node health check interval
- `secondary_nodes_idle_threshold_secs` - Secondary node idle threshold
- `site_nodes_keepalive_enabled` - Enable/disable site node health checks
- `site_nodes_keepalive_interval_secs` - Site node health check interval
- `site_nodes_idle_threshold_secs` - Site node idle threshold
- `peer_redis_keepalive_enabled` - Enable/disable peer Redis health checks
- `peer_redis_keepalive_interval_secs` - Peer Redis health check interval
- `peer_redis_idle_threshold_secs` - Peer Redis idle threshold

#### Authentication Configuration
Authentication settings can be updated at runtime:
- `auth_enabled` - Enable/disable authentication
- `auth_mode` - Change between "connection_only" or "per_request"
- `auth_token_expiry_enabled` - Enable/disable token expiry
- `session_duration_secs` - Session timeout duration

#### Performance Configuration
Performance tuning settings can be updated at runtime:
- `max_retries` - Maximum retry attempts
- `retry_delay_ms` - Delay between retries
- `batch_flush_interval_ms` - Batch flush interval
- `max_batch_size` - Maximum batch size
- `replication_batch_max_age_secs` - Batch age limit
- `config_watch_interval_secs` - How often to check for configuration file changes (1-300 seconds)

### How Hot Reload Works

1. **File Monitoring**: RustyCluster monitors configuration files for changes at configurable intervals (default: 5 seconds)
2. **Change Detection**: When a file modification is detected, the configuration is reloaded
3. **Validation**: New configuration is validated before applying changes
4. **Selective Updates**: Only changed settings are updated, minimizing service disruption
5. **Logging**: All configuration changes are logged with timestamps and version numbers

### Using Hot Configuration Reload

#### File Modification Method
Simply edit your configuration file and save it. RustyCluster will automatically detect and apply the changes:

```bash
# Edit the configuration file
nano config.toml

# Change rate limiting settings
rate_limiting_enabled = true
rate_limit_requests_per_second = 500  # Increase from 100 to 500

# Save the file - changes will be applied automatically within the configured watch interval
```

### Configuration Change Notifications

RustyCluster logs all configuration changes:

```
INFO Configuration file changed, reloading: config.toml
INFO Rate limiter configuration updated successfully
INFO Configuration reloaded successfully, version: 5
```

### Limitations

#### Settings Requiring Restart
Some settings require a full server restart to take effect:
- Redis connection URL and pool settings
- Secondary node URLs and replication settings
- Server port and network settings
- Worker thread count and runtime settings
- Write consistency and site replication settings

#### Best Practices
1. **Test Changes**: Test configuration changes in a development environment first
2. **Monitor Logs**: Watch the logs for configuration change confirmations
3. **Backup Configs**: Keep backups of working configurations
4. **Gradual Changes**: Make incremental changes rather than large modifications
5. **Validate Syntax**: Ensure TOML syntax is correct before saving

### Example: Dynamic Rate Limiting Adjustment

```toml
# Initial configuration
rate_limiting_enabled = true
rate_limit_type = "per_client"
rate_limit_requests_per_second = 100
rate_limit_burst_size = 20

# During high traffic, increase limits without restart:
rate_limit_requests_per_second = 500  # 5x increase
rate_limit_burst_size = 100           # 5x increase

# Switch to global rate limiting for better control:
rate_limit_type = "global"
rate_limit_global_requests_per_second = 2000
rate_limit_global_burst_size = 500

# Disable rate limiting temporarily:
rate_limiting_enabled = false

# Adjust configuration monitoring frequency:
config_watch_interval_secs = 10  # Check every 10 seconds instead of 5
```

### Configuring File Monitoring Frequency

You can adjust how often RustyCluster checks for configuration file changes:

```toml
# Configuration file monitoring settings
config_watch_interval_secs = 5  # Default: check every 5 seconds

# For high-frequency changes (faster detection):
config_watch_interval_secs = 1  # Check every second (minimum)

# For lower resource usage (slower detection):
config_watch_interval_secs = 30  # Check every 30 seconds

# Maximum allowed interval:
config_watch_interval_secs = 300  # Check every 5 minutes (maximum)
```

**Note**: The `config_watch_interval_secs` setting is itself hot-reloadable, so you can change the monitoring frequency without restarting the server!

## Running the Application

1. Start Redis servers for each node:
```bash
# Without authentication
redis-server --port 6379  # For primary node
redis-server --port 6370  # For secondary node 1
redis-server --port 6371  # For secondary node 2

# With authentication (example)
redis-server --port 6379 --requirepass "your_password"  # For primary node
redis-server --port 6370 --requirepass "your_password"  # For secondary node 1
redis-server --port 6371 --requirepass "your_password"  # For secondary node 2
```

2. Start the nodes with their respective configuration files:
```bash
# Start primary node
cargo run --release config.toml

# Start secondary nodes
cargo run --release config_node2.toml
cargo run --release config_node3.toml
```

## API Reference

### Authentication Operations
- `Authenticate(username, password)`: Authenticate client and receive session token

### String Operations
- `Set(key, value)`: Set a key-value pair
- `Get(key)`: Retrieve a value by key
- `Delete(key)`: Delete a key
- `SetEx(key, value, ttl)`: Set a key-value pair with expiration
- `SetExpiry(key, ttl)`: Set expiration for an existing key
- `SetNX(key, value)`: Set key only if it does not exist
- `Exists(key)`: Check if a key exists
- `DelMultiple(keys)`: Delete multiple keys in a single operation

### Numeric Operations
- `IncrBy(key, value)`: Increment a numeric value
- `DecrBy(key, value)`: Decrement a numeric value
- `IncrByFloat(key, value)`: Increment a floating-point value

### Hash Operations
- `HSet(key, field, value)`: Set a field in a hash
- `HGet(key, field)`: Get a field from a hash
- `HGetAll(key)`: Get all fields from a hash
- `HMSet(key, fields)`: Set multiple hash fields and values in a single operation
- `HExists(key, field)`: Check if a hash field exists
- `HIncrBy(key, field, value)`: Increment a numeric field
- `HDecrBy(key, field, value)`: Decrement a numeric field
- `HIncrByFloat(key, field, value)`: Increment a floating-point field
- `HDel(key, fields)`: Delete one or more hash fields
- `HScan(key, cursor, pattern, count)`: Iterate through hash fields and values with pagination
- `HLen(key)`: Get the number of fields in a hash

### Set Operations
- `SAdd(key, members)`: Add one or more members to a set
- `SMembers(key)`: Get all members of a set

### Script Operations
- `LoadScript(script)`: Load a Lua script and return its SHA hash
- `EvalSha(sha, keys, args)`: Execute a Lua script by its SHA hash

### Batch Operations
- `BatchWrite(operations)`: Execute multiple operations in a single batch for improved performance

### Health Check Operations
- `Ping()`: Check server connectivity and health status

## Recent Upgrades and Improvements

### Version 2.2.3 - Major Feature Enhancements

#### 🆕 **New Redis Operations**
- **HMSET**: Set multiple hash fields and values in a single atomic operation
- **SETNX**: Set key only if it does not exist (conditional set)
- **EXISTS**: Check if a key exists in the database
- **HEXISTS**: Check if a specific field exists in a hash
- **LOAD_SCRIPT**: Load Lua scripts and return SHA hash for execution
- **EVALSHA**: Execute Lua scripts by SHA hash with keys and arguments
- **HDEL**: Delete one or more hash fields in a single operation
- **HSCAN**: Iterate through hash fields and values with cursor-based pagination
- **HLEN**: Get the number of fields in a hash
- **SADD**: Add one or more members to a set with automatic deduplication
- **SMEMBERS**: Get all members of a set
- **DEL_MULTIPLE**: Delete multiple keys in a single atomic operation

#### 🔄 **Enhanced Replication System**
- **Full Replication Support**: All new operations support both local node replication and site replication
- **Improved Encoding**: Enhanced data encoding/decoding for complex operations (HMSET, EVALSHA)
- **Batch Processing**: All operations integrated with efficient batch processing system
- **Consistency Guarantees**: Maintains ACID properties across all replication scenarios

#### ⚡ **Performance Improvements**
- **Optimized Connection Pooling**: Enhanced Redis and gRPC connection management
- **Async Processing**: Improved asynchronous operation handling with tokio::spawn
- **Batch Optimization**: More efficient batch processing for high-throughput scenarios
- **Memory Management**: Reduced memory allocations and improved garbage collection

#### 🔧 **Infrastructure Upgrades**
- **Redis 0.31**: Upgraded from Redis 0.24 to 0.31 for better performance and features
- **Deadpool-Redis 0.20**: Upgraded from 0.14 to 0.20 for improved connection pooling
- **Tracing Integration**: Migrated from log4rs to tracing crate for better performance
- **Protocol Buffer Updates**: Enhanced protobuf definitions with new operation types

#### 🛡️ **Security and Reliability**
- **Enhanced Authentication**: Improved session management and token validation
- **Error Handling**: Better error propagation and recovery mechanisms
- **Health Monitoring**: Comprehensive health checks for all system components
- **Logging Improvements**: Structured logging with configurable patterns and file rotation

#### 📊 **Monitoring and Observability**
- **Tokio Console Support**: Real-time profiling and debugging capabilities
- **Performance Metrics**: Enhanced monitoring of RPS, latency, and throughput
- **Structured Logging**: Better log organization with contextual information
- **Health Dashboards**: Comprehensive system health monitoring

### Compatibility Notes
- **Backward Compatible**: All existing operations continue to work without changes
- **Configuration Updates**: New optional configuration parameters for enhanced features
- **Client Libraries**: Existing client code remains compatible with new server version

## Usage Examples

### New Redis Operations Examples

#### HMSET - Set Multiple Hash Fields
```bash
# Using grpcurl to set multiple hash fields at once
grpcurl -plaintext -d '{
  "key": "user:1001",
  "fields": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": "30",
    "city": "New York"
  }
}' localhost:50051 rustycluster.KeyValueService.HMSet
```

#### SETNX - Conditional Set
```bash
# Set key only if it doesn't exist
grpcurl -plaintext -d '{
  "key": "lock:resource1",
  "value": "locked_by_process_123"
}' localhost:50051 rustycluster.KeyValueService.SetNX
```

#### EXISTS and HEXISTS - Check Existence
```bash
# Check if key exists
grpcurl -plaintext -d '{
  "key": "user:1001"
}' localhost:50051 rustycluster.KeyValueService.Exists

# Check if hash field exists
grpcurl -plaintext -d '{
  "key": "user:1001",
  "field": "email"
}' localhost:50051 rustycluster.KeyValueService.HExists
```

#### Lua Script Operations
```bash
# Load a Lua script
grpcurl -plaintext -d '{
  "script": "return redis.call('\''get'\'', KEYS[1]) or '\''default'\''"
}' localhost:50051 rustycluster.KeyValueService.LoadScript

# Execute script by SHA (replace with actual SHA from LoadScript response)
grpcurl -plaintext -d '{
  "sha": "your_script_sha_here",
  "keys": ["mykey"],
  "args": []
}' localhost:50051 rustycluster.KeyValueService.EvalSha
```

#### Batch Operations
```bash
# Execute multiple operations in a single batch
grpcurl -plaintext -d '{
  "operations": [
    {
      "operation_type": 0,
      "key": "key1",
      "value": "value1"
    },
    {
      "operation_type": 11,
      "key": "hash1",
      "hash_fields": {
        "field1": "value1",
        "field2": "value2"
      }
    }
  ]
}' localhost:50051 rustycluster.KeyValueService.BatchWrite
```

## Write Consistency and Site Replication

### Write Consistency Overview

Write consistency ensures that write operations are successfully replicated to a configurable number of peer Redis nodes before responding to the client. This feature is only active when `async_replication = false`.

#### How Write Consistency Works

1. **Client sends write request** to Rust node
2. **Rust node writes to local Redis** first
3. **Rust node writes to peer Redis nodes** directly (not through other Rust nodes)
4. **Rust node waits for responses** from all peer Redis nodes
5. **Rust node counts successful responses**
6. **Rust node checks write consistency**:
   - `ALL`: Success only if all peer Redis nodes responded successfully
   - `QUORUM`: Success if majority (or `quorum_value`) responded successfully
   - `ONE`: Success if at least one peer Redis node responded successfully
7. **Rust node responds to client** with success/failure based on consistency check

#### Write Consistency Configuration

```toml
# IMPORTANT: Set to false to enable write consistency
async_replication = false

# List of peer Redis nodes for write consistency
peer_redis_nodes = ["redis://username:password@host1:port", "redis://username:password@host2:port"]

# Write consistency level: ALL, QUORUM, or ONE
write_consistency = "QUORUM"

# Number of nodes expected for quorum (used when write_consistency = "QUORUM")
quorum_value = 2

# Number of retry attempts for write consistency operations
write_retry_count = 3

# Connection pool size for each peer Redis node
peer_redis_pool_size = 64
```

### Site Replication Overview

Site replication provides cross-site data replication capabilities for disaster recovery and geographic distribution. Site replication only works when `async_replication = true`.

#### How Site Replication Works

1. **Client sends write request** to local Rust node
2. **Local Rust node writes to local Redis** first
3. **Local Rust node responds to client immediately** (async replication)
4. **Local Rust node replicates to secondary nodes** asynchronously
5. **Local Rust node replicates to other sites** by sending gRPC requests to primary nodes in other sites
6. **Site replication uses `skip_replication = false`** so remote sites replicate to their own nodes
7. **Site replication uses `skip_site_replication = true`** to prevent site replication loops

#### Site Replication Features

- **Primary/Failover Configuration**: Configure primary nodes for each site with failover nodes as backup
- **Automatic Failover**: Automatically switches to failover nodes when primary nodes are unavailable
- **Non-blocking**: Site replication failures don't block client responses (local write still succeeds)
- **Retry Logic**: Configurable retry attempts with exponential backoff for site replication
- **Health Tracking**: Tracks availability of site nodes and marks them as available/unavailable
- **Complete Operation Support**: Replicates all write operations including SET, DELETE, SETEX, SETEXPIRY, INCRBY, DECRBY, INCRBYFLOAT, HSET, HINCRBY, HDECRBY, HINCRBYFLOAT, HMSET, SETNX, EXISTS, HEXISTS, LOAD_SCRIPT, and EVALSHA

#### Site Replication Configuration

```toml
# IMPORTANT: Set to true to enable site replication
async_replication = true

# Site replication configuration
site_replication_enabled = true
site_replication_nodes = [
    { primary = "http://site2-primary:50053", failover = "http://site2-failover:50054" },
    { primary = "http://site3-primary:50053", failover = "http://site3-failover:50054" }
]
site_replication_retry_count = 3
site_replication_timeout_ms = 5000
```

## Authentication and Security

### Authentication Modes

RustyCluster supports two authentication modes to optimize performance based on your security requirements:

1. **per_request** (default): Validates session token on each request
2. **connection_only**: Authenticates once per connection, no token validation per request

#### Authentication Configuration

```toml
# Authentication mode: "connection_only" or "per_request"
auth_mode = "connection_only"

# Enable/disable session token expiry
auth_token_expiry_enabled = false

# Session duration in seconds (only applies when expiry is enabled)
session_duration_secs = 3600
```

#### Authentication Mode Comparison

| Mode | RPS Impact | Security Level | Recommended For |
|------|------------|----------------|-----------------|
| per_request | Baseline | High | Production, untrusted networks |
| connection_only | +10-15% | Medium | High-performance, trusted networks |

### Security Features

- **Username/Password Authentication**: Secure client authentication with session management
- **Session Token Management**: Configurable token expiry and validation
- **Redis ACL Support**: Support for Redis 6.0+ ACL and legacy password authentication
- **Authentication Bypass for Internal Operations**: Health checks and replication operations bypass authentication
- **Secure Inter-node Communication**: Authentication not required between RustyCluster nodes

## Rate Limiting

### Overview

RustyCluster includes a high-performance rate limiting system designed to prevent abuse and ensure fair resource usage without impacting performance. The rate limiter uses a token bucket algorithm with per-client tracking and lock-free atomic operations.

### Key Features

- **Zero Overhead When Disabled**: Rate limiting checks are completely bypassed when disabled
- **Multiple Rate Limiting Types**: Support for global, per-client, or both rate limiting modes
- **Global Rate Limiting**: Limit total requests across all clients to protect server resources
- **Per-Client Rate Limiting**: Each client (identified by IP address or session token) has independent rate limits
- **Combined Rate Limiting**: Apply both global and per-client limits simultaneously
- **Token Bucket Algorithm**: Allows burst traffic while maintaining average rate limits
- **Lock-Free Implementation**: Uses atomic operations and DashMap for maximum performance
- **Automatic Cleanup**: Expired client entries are automatically removed to prevent memory leaks
- **Configurable Limits**: Flexible configuration for different use cases

### Rate Limiting Configuration

```toml
# Enable/disable rate limiting (default: false)
rate_limiting_enabled = false

# Rate limiting type: "global", "per_client", or "both" (default: "per_client")
# - "global": Limit total requests across all clients
# - "per_client": Limit requests per individual client
# - "both": Apply both global and per-client limits (both must pass)
rate_limit_type = "per_client"

# Per-client rate limiting settings (used when rate_limit_type = "per_client" or "both")
# Maximum requests per second per client (default: 1000)
rate_limit_requests_per_second = 1000

# Per-client maximum burst size - allows short bursts above the rate limit (default: 100)
# Higher values allow more bursty traffic but may impact performance under sustained load
rate_limit_burst_size = 100

# Global rate limiting settings (used when rate_limit_type = "global" or "both")
# Maximum requests per second across all clients (default: 10000)
rate_limit_global_requests_per_second = 10000

# Global maximum burst size - allows short bursts above the rate limit (default: 1000)
# Higher values allow more bursty traffic but may impact performance under sustained load
rate_limit_global_burst_size = 1000

# Cleanup interval for expired client rate limiters in seconds (default: 300)
# Clients that haven't made requests for 2x this interval will be removed from memory
# Lower values use less memory but require more CPU for cleanup
rate_limit_cleanup_interval_secs = 300
```

### Rate Limiting Types

#### Per-Client Rate Limiting (`rate_limit_type = "per_client"`)
- Each client has independent rate limits based on their IP address or session token
- Allows fair resource sharing among clients
- Prevents any single client from overwhelming the server
- Total server capacity = `rate_limit_requests_per_second × number_of_clients`

#### Global Rate Limiting (`rate_limit_type = "global"`)
- Limits total requests across all clients combined
- Protects server resources under high load
- All clients compete for the same global token bucket
- Total server capacity = `rate_limit_global_requests_per_second`

#### Combined Rate Limiting (`rate_limit_type = "both"`)
- Applies both global and per-client limits simultaneously
- Both limits must pass for a request to be allowed
- Provides comprehensive protection against abuse
- Ensures fair distribution while protecting server resources

### How Rate Limiting Works

1. **Client Identification**: Clients are identified by IP address (preferred) or session token hash
2. **Token Bucket Algorithm**:
   - **Per-Client**: Each client gets a token bucket with configurable capacity and refill rate
   - **Global**: All clients share a single global token bucket
   - **Both**: Requests must pass both global and per-client token bucket checks
3. **Request Processing**: Each request consumes one token from the appropriate bucket(s)
4. **Rate Limiting**: If no tokens are available, the request is rejected with `RESOURCE_EXHAUSTED` status
5. **Token Refill**: Tokens are refilled at the configured rate (requests per second)
6. **Burst Handling**: The bucket capacity allows short bursts above the average rate
7. **Cleanup**: Inactive clients are automatically removed to prevent memory growth

### Performance Impact

- **Disabled**: Zero performance impact when `rate_limiting_enabled = false`
- **Enabled**: Minimal impact (~1-2% overhead) due to lock-free design
- **Memory Usage**: ~100 bytes per active client
- **CPU Usage**: Negligible due to atomic operations and efficient cleanup

### Rate Limiting Behavior

#### When Rate Limit is Exceeded

```
Status: RESOURCE_EXHAUSTED
Message: "Rate limit exceeded"
```

#### Exempt Operations

- **Health Checks**: Internal health checks bypass rate limiting
- **Node Replication**: Inter-node replication requests bypass rate limiting
- **Site Replication**: Cross-site replication requests bypass rate limiting

### Example Configurations

#### High-Performance Setup (Per-Client)
```toml
rate_limiting_enabled = true
rate_limit_type = "per_client"
rate_limit_requests_per_second = 5000  # 5K RPS per client
rate_limit_burst_size = 500            # Allow 500 request bursts
rate_limit_cleanup_interval_secs = 600 # 10 minute cleanup
```

#### Global Rate Limiting (Server Protection)
```toml
rate_limiting_enabled = true
rate_limit_type = "global"
rate_limit_global_requests_per_second = 15000  # 15K RPS globally
rate_limit_global_burst_size = 1500            # Allow 1500 request bursts
rate_limit_cleanup_interval_secs = 300         # 5 minute cleanup
```

#### Combined Rate Limiting (Maximum Protection)
```toml
rate_limiting_enabled = true
rate_limit_type = "both"
# Per-client limits
rate_limit_requests_per_second = 1000  # 1K RPS per client
rate_limit_burst_size = 100            # Allow 100 request bursts per client
# Global limits
rate_limit_global_requests_per_second = 10000  # 10K RPS globally
rate_limit_global_burst_size = 1000            # Allow 1000 request bursts globally
rate_limit_cleanup_interval_secs = 300         # 5 minute cleanup
```

#### Conservative Setup (Protection Against Abuse)
```toml
rate_limiting_enabled = true
rate_limit_type = "per_client"
rate_limit_requests_per_second = 100   # 100 RPS per client
rate_limit_burst_size = 20             # Allow 20 request bursts
rate_limit_cleanup_interval_secs = 180 # 3 minute cleanup
```

### Monitoring Rate Limiting

Rate limiting events are logged with appropriate levels:

```
# Rate limit exceeded (WARN level)
WARN Rate limit exceeded for client: 192.168.1.100

# Rate limit check passed (DEBUG level)
DEBUG Rate limit check passed for client: 192.168.1.100

# Client rate limiter created (DEBUG level)
DEBUG Creating new rate limiter for client: 192.168.1.100

# Cleanup events (INFO level)
INFO Rate limiter cleanup: removed 5 expired clients, active clients: 25
```

## Health Monitoring and Keep-Alive

### Health Check Overview

RustyCluster includes comprehensive health check mechanisms to maintain connection health during idle periods and prevent connection timeouts.

### Supported Connection Types

1. **Redis Connections**: Primary Redis instance
2. **Secondary Nodes**: Rust node connections for replication
3. **Site Replication Nodes**: Cross-site replication connections
4. **Peer Redis Nodes**: Direct Redis connections for write consistency

### Health Check Configuration

```toml
# Redis connection health checks
redis_keepalive_enabled = true
redis_keepalive_interval_secs = 30

# Secondary nodes health checks
secondary_nodes_keepalive_enabled = true
secondary_nodes_keepalive_interval_secs = 30

# Site replication nodes health checks
site_nodes_keepalive_enabled = true
site_nodes_keepalive_interval_secs = 30

# Peer Redis nodes health checks
peer_redis_keepalive_enabled = true
peer_redis_keepalive_interval_secs = 30

# Rate limiting configuration
rate_limiting_enabled = false
rate_limit_requests_per_second = 1000
rate_limit_burst_size = 100
rate_limit_cleanup_interval_secs = 300
```

### Health Check Benefits

1. **Connection Stability**: Prevents idle connection timeouts
2. **Performance**: Reduces connection re-establishment overhead
3. **Reliability**: Early detection of connection issues
4. **Monitoring**: Health status logging for troubleshooting

## Performance Considerations

### Connection Management
- **Multi-tier Connection Pooling**: Separate pools for Redis, secondary nodes, peer Redis nodes, and site replication
- **Physical Connections**: Option to use actual TCP connections instead of HTTP/2 multiplexing for better monitoring
- **Configurable Pool Sizes**: Fine-tuned pool sizes for different connection types based on workload requirements
- **Connection Health Monitoring**: Automatic health checks and connection recovery

### Replication Performance
- **Asynchronous Replication**: Non-blocking replication to secondary nodes for improved client response times
- **Batch Processing**: Intelligent batching of operations for efficient network utilization
- **Parallel Processing**: Concurrent replication to multiple nodes with controlled concurrency limits
- **Site Replication Optimization**: Efficient cross-site replication with failover support

### Operation Efficiency
- **Batch Operations**: Single-request multiple operations for reduced network overhead
- **Memory Optimization**: Reduced allocations and improved garbage collection patterns
- **Async Processing**: Tokio-based async runtime for maximum concurrency
- **Lock-free Operations**: Minimized contention in high-throughput scenarios

### Performance Metrics
- **Target Performance**: 10,000+ RPS with replication enabled
- **Latency Optimization**: Sub-millisecond operation latencies under normal load
- **Scalability**: Linear scaling with additional nodes and Redis instances
- **Resource Efficiency**: Optimized CPU and memory usage patterns

### Monitoring and Tuning
- **Real-time Metrics**: Integration with Tokio Console for live performance monitoring
- **Structured Logging**: Performance-optimized logging with minimal overhead
- **Health Dashboards**: Comprehensive monitoring of all system components
- **Configurable Thresholds**: Adjustable performance parameters for different environments

## Load Testing and Performance Optimization

### High-Performance Load Testing

RustyCluster includes comprehensive load testing tools to achieve and validate 10,000+ RPS performance targets.

#### Quick Start: Achieve 10K+ RPS

1. **Start RustyCluster with Performance Config**
```bash
# From the main rustycluster directory
cargo run --release load/config_performance.toml
```

2. **Run Optimized Load Test**
```bash
# Navigate to load directory
cd load

# Try the ultra-high performance async version
python loadtest_async.py --username testuser --password testpass --operations 200000 --concurrency 2000 --rps 10000 --pool-size 200
```

3. **Scale Up if Successful**
```bash
# If 10K works, try higher
python loadtest_async.py --username testuser --password testpass --operations 300000 --concurrency 3000 --rps 15000 --pool-size 300

# Push to maximum
python loadtest_async.py --username testuser --password testpass --operations 500000 --concurrency 5000 --rps 20000 --pool-size 500
```

#### Available Load Testing Scripts

| Script | Expected RPS | Technology | Best Use Case |
|--------|--------------|------------|---------------|
| `loadtest_async.py` | 10K-20K+ | Asyncio | Maximum performance |
| `loadtest_optimized.py` | 5K-10K | Threading + pooling | High performance |
| `loadtest_with_auth.py` | 2K-5K | Basic threading | Basic testing |

#### Performance Configuration Files

- **`config_performance.toml`**: Optimized for maximum throughput, no replication overhead
- **`config_auth_test.toml`**: Standard configuration with authentication
- **`config_loadtest.toml`**: Authentication disabled for baseline testing

### Site Replication Performance Optimization

#### Performance Analysis

**Current Performance**: 6000 TPS with site replication enabled
**Target**: 10000+ RPS
**Gap**: ~67% performance improvement needed

#### Root Cause Analysis

1. **Sequential Operation Processing**: Operations sent one-by-one sequentially
2. **Mutex Contention**: Site replication manager serialization
3. **Inefficient Batching**: Small batches sent frequently
4. **Suboptimal Connection Pool Usage**: Sequential processing doesn't utilize pools effectively

#### Optimization Strategy

**Phase 1: Configuration Optimizations**
```toml
# Optimized batch flush interval
batch_flush_interval_ms = 100

# Increased site replication pool
site_replication_pool_size = 1024

# Reduced retry count for faster failure detection
site_replication_retry_count = 2

# Reduced timeout for faster operations
site_replication_timeout_ms = 500

# Enable physical connections for better load balancing
use_physical_connections = true
```

**Phase 2: Code Optimizations**
- **BatchWrite Implementation**: Use single network round trip for entire batch instead of N round trips
- **Parallel Operation Processing**: Send operations in parallel instead of sequentially
- **Lock-Free Site Replication Manager**: Remove mutex contention
- **Connection Pool Optimization**: Utilize multiple clients in parallel

#### Expected Performance Improvements

| Optimization Phase | Expected RPS Gain | Cumulative RPS |
|-------------------|------------------|----------------|
| **Baseline**      | -                | 6000 RPS       |
| **Phase 1: Config** | +1000-2000     | 7000-8000 RPS |
| **Phase 2: Code**   | +2000-4000     | 9000-12000 RPS |
| **🎯 TARGET**     | **+3000-6000** | **9000-12000 RPS** |

### Performance Monitoring

#### Key Metrics to Watch
1. **Throughput**: Target ≥10000 RPS
2. **Latency**: P95 <100ms, P99 <200ms
3. **Error Rate**: <1%
4. **Connection Utilization**: Monitor actual vs configured pool sizes

#### Success Criteria
- ✅ **Excellent**: ≥10000 RPS (≥100% of target)
- ✅ **Good**: ≥9500 RPS (≥95% of target)
- ⚠️ **Acceptable**: ≥8000 RPS (≥80% of target)
- ❌ **Needs Work**: <8000 RPS (<80% of target)

### Expected Results

#### With Authentication Enabled
- **Target**: 10,000 RPS
- **Achievable**: 10,000-20,000 RPS
- **Latency**: 1-5ms average

#### Without Authentication
- **Target**: 15,000+ RPS
- **Achievable**: 15,000-25,000 RPS
- **Latency**: 0.5-3ms average

### System Requirements

#### Client Machine
- **CPU**: 4+ cores recommended
- **RAM**: 4GB+ available
- **Network**: Gigabit connection preferred
- **Python**: 3.7+ with grpcio

#### Server Machine (RustyCluster)
- **CPU**: 8+ cores for 10K+ RPS
- **RAM**: 8GB+ recommended
- **Redis**: Properly configured and tuned
- **Network**: Low latency to client

## Troubleshooting

### Performance Issues

#### Issue: Low RPS (< 5K)
**Solutions:**
1. Use `loadtest_async.py` for maximum performance
2. Increase concurrency: `--concurrency 2000`
3. Increase pool size: `--pool-size 200`
4. Verify RustyCluster is using performance config
5. Check system resources (CPU, memory, network)

#### Issue: High Latency
**Check:**
- Network latency: `ping 127.0.0.1`
- Redis performance: `redis-cli --latency-history`
- CPU usage: `htop`
- Connection pool utilization

#### Issue: Connection Errors
**Solutions:**
- Reduce concurrency, increase pool size
- Increase timeouts in client configuration
- Check network connectivity and firewall settings
- Monitor connection pool statistics

### Authentication Issues

#### Issue: Authentication Failures
**Check:**
1. `auth_mode` setting matches client expectations
2. `auth_token_expiry_enabled` setting
3. Session creation/validation logs
4. Username/password correctness

#### Issue: Session Token Expiry
**Solutions:**
- Increase `session_duration_secs`
- Set `auth_token_expiry_enabled = false` for testing
- Implement token refresh in client applications

### Replication Issues

#### Issue: Site Replication Not Working
**Check:**
1. `async_replication = true` (required for site replication)
2. `site_replication_enabled = true`
3. Site nodes are accessible and running
4. Network connectivity between sites
5. Authentication bypass for site replication

#### Issue: Write Consistency Failures
**Check:**
1. `async_replication = false` (required for write consistency)
2. Peer Redis nodes are accessible
3. Redis authentication credentials
4. Network connectivity to peer nodes
5. Consistency level configuration (ALL/QUORUM/ONE)

### Health Check Issues

#### Issue: Health Check Failures
**Normal Behavior:**
- Authentication failures are expected when auth is enabled
- Check for connection success logs, not auth failures

**Solutions:**
- Verify all configured nodes are reachable
- Adjust health check intervals based on network stability
- Enable health checks for all connection types
- Check firewall and network connectivity

### Configuration Issues

#### Issue: Configuration Not Loading
**Check:**
1. Configuration file path is correct
2. TOML syntax is valid
3. All required parameters are present
4. File permissions allow reading

#### Issue: Redis Connection Failures
**Check:**
1. Redis server is running
2. Redis authentication credentials
3. Network connectivity to Redis
4. Redis configuration (bind address, port)
5. Connection pool settings

### Monitoring and Debugging

#### Enable Debug Logging
```toml
# Add to config.toml
level = "debug"
console_enabled = true
```

#### Monitor Connection Pools
```bash
# Monitor actual connections
netstat -an | grep :50051 | wc -l
netstat -an | grep :6379 | wc -l
```

#### Check Resource Usage
```bash
# CPU and memory usage
top -p $(pgrep rustycluster)

# Network usage
iftop
```

#### Verify Site Nodes
```bash
# Check if site nodes are accessible
curl -v http://127.0.0.1:50053/health
curl -v http://127.0.0.1:50054/health
```

## Error Handling

- Automatic retries for failed operations
- Configurable retry count and delay
- Detailed error logging
- Graceful handling of node failures

## Monitoring and Tracing

RustyCluster uses the `tracing` crate for structured logging and diagnostics. This provides several benefits:

- **Structured logging**: Better organization of log data with contextual information
- **Spans**: Track operations across asynchronous boundaries
- **Performance**: More efficient logging with less overhead
- **Integration with Tokio**: Better integration with the Tokio ecosystem

### Configuration

Tracing is configured using a TOML file (default: `logconfig.toml`):

```toml
# Tracing configuration file for RustyCluster
file = "rustycluster.log"
max_size = 10485760  # 10MB
max_files = 5
level = "info"
pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} - {l} - {M} - {m}\n"
console_enabled = false
```

### Environment Variables

You can override the log level using the `RUST_LOG` environment variable:

```bash
RUST_LOG=debug cargo run --release config.toml
```

### Tokio Console Integration

RustyCluster supports [Tokio Console](https://github.com/tokio-rs/console) for real-time profiling and debugging. To enable it:

1. Build with the console feature:
```bash
cargo build --release --features console
```

2. Set `console_enabled = true` in your `logconfig.toml` file

3. Run with the `tokio_unstable` flag:
```bash
RUSTFLAGS="--cfg tokio_unstable" cargo run --release --features console config.toml
```

4. In another terminal, run the Tokio Console:
```bash
cargo install --locked tokio-console
tokio-console
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.