use std::path::PathBuf;
use std::sync::Arc;
use std::sync::Mutex;
use serde::Deserialize;
use config::Config as AppConfig;
use tracing::Level;
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    prelude::*,
    Env<PERSON><PERSON>er,
    <PERSON><PERSON>,
};
use tracing_appender::{
    non_blocking::WorkerGuard,
};
use tracing_log::LogTracer;

// Global guard to keep the non-blocking writer alive
lazy_static::lazy_static! {
    static ref GLOBAL_GUARD: Arc<Mutex<Option<WorkerGuard>>> = Arc::new(Mutex::new(None));
}

#[derive(Deserialize, Debug, Clone)]
#[allow(dead_code)]  // Fields are used via deserialization
pub struct TracingParams {
    #[serde(default = "default_log_file")]
    pub file: String,
    #[serde(default = "default_max_size")]
    pub max_size: u64,
    #[serde(default = "default_max_files")]
    pub max_files: u32,
    #[serde(default = "default_log_level")]
    pub level: String,
    #[serde(default = "default_pattern")]
    pub pattern: String,
    #[serde(default = "default_console_enabled")]
    pub console_enabled: bool,
}

fn default_log_file() -> String {
    "rustycluster.log".to_string()
}

fn default_max_size() -> u64 {
    10 * 1024 * 1024 // 10MB
}

fn default_max_files() -> u32 {
    5
}

fn default_log_level() -> String {
    "info".to_string()
}

fn default_pattern() -> String {
    "{d(%Y-%m-%d %H:%M:%S.%3f)} - {l} - {M} - {m}\n".to_string()
}

fn default_console_enabled() -> bool {
    false
}

impl Default for TracingParams {
    fn default() -> Self {
        Self {
            file: default_log_file(),
            max_size: default_max_size(),
            max_files: default_max_files(),
            level: default_log_level(),
            pattern: default_pattern(),
            console_enabled: default_console_enabled(),
        }
    }
}

impl TracingParams {
    pub fn from_file(file_path: Option<&str>) -> Result<Self, Box<dyn std::error::Error>> {
        let config_file = file_path.unwrap_or("logconfig.toml");
        let settings = AppConfig::builder()
            .add_source(config::File::with_name(config_file).required(false))
            .build()?;

        let params: TracingParams = settings.try_deserialize()?;
        Ok(params)
    }
}

// This function is kept for future use if needed
#[allow(dead_code)]
fn parse_level(level_str: &str) -> Level {
    match level_str.to_lowercase().as_str() {
        "trace" => Level::TRACE,
        "debug" => Level::DEBUG,
        "info" => Level::INFO,
        "warn" => Level::WARN,
        "error" => Level::ERROR,
        _ => Level::INFO,
    }
}

// Setup tracing with file appender and optional console subscriber
#[cfg(feature = "console")]
pub fn setup_tracing(params: &TracingParams) -> Result<(), Box<dyn std::error::Error>> {
    // Initialize LogTracer to convert log crate records to tracing events
    LogTracer::init()?;

    // Check if the log directory exists and is writable
    let log_path = PathBuf::from(&params.file);
    let default_dir = PathBuf::from(".");
    let log_dir = log_path.parent().unwrap_or(&default_dir);

    // Create the directory if it doesn't exist
    if !log_dir.exists() {
        if let Err(e) = std::fs::create_dir_all(log_dir) {
            eprintln!("Warning: Failed to create log directory: {}", e);
        }
    }

    // Check if we can write to the log file
    let test_file_path = log_dir.join("test_write.tmp");
    match std::fs::File::create(&test_file_path) {
        Ok(_) => {
            // Clean up the test file
            let _ = std::fs::remove_file(&test_file_path);
            eprintln!("Log directory is writable: {}", log_dir.display());
        },
        Err(e) => {
            eprintln!("Warning: Log directory may not be writable: {}", e);
        }
    }

    // Get the filename from the path or use a default
    let filename = log_path.file_name()
        .unwrap_or_else(|| std::ffi::OsStr::new("rustycluster.log"));

    eprintln!("Setting up log file: {}/{}", log_dir.display(), filename.to_string_lossy());

    // Create a simple file appender
    let log_file_path = log_dir.join(filename);

    // Try to open the file directly to ensure it works
    let file = match std::fs::OpenOptions::new()
        .create(true)
        .write(true)
        .append(true)
        .open(&log_file_path) {
        Ok(file) => {
            eprintln!("Successfully opened log file for appender: {}", log_file_path.display());
            file
        },
        Err(e) => {
            eprintln!("Error opening log file for appender: {} - {}", log_file_path.display(), e);
            // Create a fallback file in the current directory
            let fallback_path = PathBuf::from("rustycluster_fallback.log");
            eprintln!("Trying fallback log file: {}", fallback_path.display());
            std::fs::OpenOptions::new()
                .create(true)
                .write(true)
                .append(true)
                .open(&fallback_path)
                .expect("Failed to open fallback log file")
        }
    };

    // Create a non-blocking writer from the file
    let (non_blocking, guard) = tracing_appender::non_blocking(std::io::BufWriter::new(file));

    // Store the guard in the global variable to keep it alive
    if let Ok(mut global_guard) = GLOBAL_GUARD.lock() {
        *global_guard = Some(guard);
    } else {
        eprintln!("Warning: Failed to store tracing guard, logs may not be written");
    }

    // Create a filter based on the configured level
    let filter_str = format!("{}", params.level.to_lowercase());
    eprintln!("Using filter: {}", filter_str);

    // Create a filter for the file layer
    let filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(&filter_str));

    // Create a formatting layer for the file appender
    // Extract the date format from the pattern
    // The pattern is used for the overall log format, but we need to extract just the date format
    // Default to a standard format if we can't extract it
    let date_format = if params.pattern.contains("{d(") {
        // Try to extract the date format from the pattern
        let start = params.pattern.find("{d(").map(|i| i + 3).unwrap_or(0);
        let end = params.pattern[start..].find(")").map(|i| i + start).unwrap_or(params.pattern.len());
        if start > 0 && end > start {
            params.pattern[start..end].to_string()
        } else {
            "%Y-%m-%d %H:%M:%S.%3f".to_string()
        }
    } else {
        "%Y-%m-%d %H:%M:%S.%3f".to_string()
    };

    let file_layer = fmt::layer()
        .with_writer(non_blocking)
        .with_ansi(false)
        .with_span_events(FmtSpan::CLOSE)
        .with_timer(tracing_subscriber::fmt::time::ChronoLocal::new(date_format));

    // If console is enabled, create a layered subscriber with both console and file
    if params.console_enabled {
        eprintln!("Tokio Console integration enabled");

        // Create a registry that will hold both the file layer and console layer
        let registry = Registry::default().with(filter);

        // First set up the file layer
        let registry = registry.with(file_layer);

        // Then add the console layer - this is a special case that needs to be handled differently
        // We use the console_subscriber's layer method instead of init()
        // Note: console_subscriber::spawn() requires tokio_unstable, which is set via RUSTFLAGS
        let registry = registry.with(console_subscriber::spawn());

        // Set the combined registry as the global default
        tracing::subscriber::set_global_default(registry)?;
    } else {
        // Create a subscriber with just the file layer
        let subscriber = Registry::default()
            .with(filter)
            .with(file_layer);

        // Set the subscriber as global default
        tracing::subscriber::set_global_default(subscriber)?;
    }

    Ok(())
}

// Setup tracing without console subscriber when the feature is not enabled
#[cfg(not(feature = "console"))]
pub fn setup_tracing(params: &TracingParams) -> Result<(), Box<dyn std::error::Error>> {
    // Initialize LogTracer to convert log crate records to tracing events
    LogTracer::init()?;

    // Check if the log directory exists and is writable
    let log_path = PathBuf::from(&params.file);
    let default_dir = PathBuf::from(".");
    let log_dir = log_path.parent().unwrap_or(&default_dir);

    // Create the directory if it doesn't exist
    if !log_dir.exists() {
        if let Err(e) = std::fs::create_dir_all(log_dir) {
            panic!("Failed to create log directory: {}", e);
        }
    }

    // Check if we can write to the log file
    let test_file_path = log_dir.join("test_write.tmp");
    match std::fs::File::create(&test_file_path) {
        Ok(_) => {
            // Clean up the test file
            let _ = std::fs::remove_file(&test_file_path);
        },
        Err(e) => {
            panic!("Log directory is not writable: {}", e);
        }
    }

    // Get the filename from the path or use a default
    let filename = log_path.file_name()
        .unwrap_or_else(|| std::ffi::OsStr::new("rustycluster.log"));

    // Create a log file path
    let log_file_path = log_dir.join(filename);

    // Try to open the file directly to ensure it works
    let file = match std::fs::OpenOptions::new()
        .create(true)
        .write(true)
        .append(true)
        .open(&log_file_path) {
        Ok(file) => file,
        Err(e) => {
            // Create a fallback file in the current directory
            let fallback_path = PathBuf::from("rustycluster_fallback.log");
            std::fs::OpenOptions::new()
                .create(true)
                .write(true)
                .append(true)
                .open(&fallback_path)
                .unwrap_or_else(|_| panic!("Failed to open log file: {}", e))
        }
    };

    // Create a non-blocking writer from the file
    let (non_blocking, guard) = tracing_appender::non_blocking(std::io::BufWriter::new(file));

    // Store the guard in the global variable to keep it alive
    if let Ok(mut global_guard) = GLOBAL_GUARD.lock() {
        *global_guard = Some(guard);
    } else {
        // If we can't store the guard, we can't guarantee logs will be written
        panic!("Failed to store tracing guard, logs may not be written");
    }

    // Create a filter based on the configured level
    let filter_str = format!("{}", params.level.to_lowercase());

    // Create a filter for the file layer
    let filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(&filter_str));

    // Create a formatting layer for the file appender
    // Extract the date format from the pattern
    // The pattern is used for the overall log format, but we need to extract just the date format
    // Default to a standard format if we can't extract it
    let date_format = if params.pattern.contains("{d(") {
        // Try to extract the date format from the pattern
        let start = params.pattern.find("{d(").map(|i| i + 3).unwrap_or(0);
        let end = params.pattern[start..].find(")").map(|i| i + start).unwrap_or(params.pattern.len());
        if start > 0 && end > start {
            params.pattern[start..end].to_string()
        } else {
            "%Y-%m-%d %H:%M:%S.%3f".to_string()
        }
    } else {
        "%Y-%m-%d %H:%M:%S.%3f".to_string()
    };

    let file_layer = fmt::layer()
        .with_writer(non_blocking)
        .with_ansi(false)
        .with_span_events(FmtSpan::CLOSE)
        .with_timer(tracing_subscriber::fmt::time::ChronoLocal::new(date_format));

    // Create a subscriber with just the file layer
    let subscriber = Registry::default()
        .with(filter)
        .with(file_layer);

    tracing::subscriber::set_global_default(subscriber)?;

    Ok(())
}
