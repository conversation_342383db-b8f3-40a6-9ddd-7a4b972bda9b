use deadpool_redis::{Manager, Pool, Runtime};
use deadpool_redis::redis::{RedisError, RedisR<PERSON>ult, AsyncCommands, cmd, pipe};
use std::collections::HashMap;
use std::io;
use std::time::Duration;
use std::sync::Arc;
use tracing::{debug, error, info, warn};

#[derive(Clone)]
pub struct RedisClient {
    async_pool: Pool, // For asynchronous operations
    // Track metrics for connection pool health
    pool_size: usize,
}

impl RedisClient {
    /// Create a Redis client with async capabilities using Manager
    ///
    /// Optimized for high-throughput scenarios with improved connection handling
    /// Supports Redis authentication if the URL contains credentials
    pub fn new(redis_url: &str, pool_size: usize) -> Result<Self, RedisError> {
        info!("Creating Redis client with pool size {}", pool_size);

        // The Redis URL is used as-is since deadpool-redis correctly handles all authentication formats:
        // - redis://username:password@host:port (username + password)
        // - redis://:password@host:port (password only)
        // - redis://username@host:port (username only - will be used as password)
        // - redis://host:port (no authentication)

        if redis_url.contains('@') {
            if redis_url.contains(':') && redis_url.split('@').next().unwrap().contains(':') {
                // Count colons to determine if it's username:password or just :password
                let auth_part = redis_url.split('@').next().unwrap();
                let colon_count = auth_part.chars().filter(|&c| c == ':').count();

                if colon_count > 1 {
                    // Format is redis://username:password@host:port
                    info!("Redis URL contains username and password authentication");
                } else {
                    // Format is redis://:password@host:port (password only)
                    info!("Redis URL contains password-only authentication");
                }
            } else {
                // Format is redis://username@host:port (username only, will be used as password)
                info!("Redis URL contains username-only authentication (will be used as password)");
            }
        } else {
            // No authentication in URL
            info!("Redis URL does not contain authentication information");
        }

        // Create a Redis manager with optimized settings
        // The Manager::new method automatically parses the URL and extracts authentication information
        let manager = Manager::new(redis_url)
            .map_err(|e| {
                error!("Invalid Redis URL: {}", e);
                RedisError::from(io::Error::new(io::ErrorKind::Other, format!("Invalid Redis URL: {}", e)))
            })?;

        // Create a connection pool with the manager and optimized settings
        let async_pool = Pool::builder(manager)
            .max_size(pool_size)
            .runtime(Runtime::Tokio1)
            .wait_timeout(Some(Duration::from_millis(50)))      // Reduced from 100ms to 50ms for faster failure detection
            .create_timeout(Some(Duration::from_secs(1)))       // Reduced from 2s to 1s for faster connection creation
            .recycle_timeout(Some(Duration::from_secs(30)))     // Reduced from 60s to 30s for more frequent recycling
            .build()
            .map_err(|e| {
                error!("Failed to build Redis connection pool: {}", e);
                RedisError::from(io::Error::new(io::ErrorKind::Other, format!("Failed to build pool: {}", e)))
            })?;

        info!("Redis client created successfully with pool size {}", pool_size);
        Ok(Self { async_pool, pool_size })
    }

    /// Helper function to get a connection from the pool with optimized error handling
    ///
    /// Uses adaptive backoff strategy with tracing for better observability
    async fn get_connection(&self) -> RedisResult<deadpool_redis::Connection> {
        // Try up to 3 times with adaptive backoff
        let mut retry_count = 0;
        let max_retries = 3;

        // Use a shorter initial delay for faster recovery
        let base_delay_ms = 2; // Start with just 2ms delay

        loop {
            match self.async_pool.get().await {
                Ok(conn) => {
                    // Only log at debug level for successful connections to reduce log volume
                    if retry_count > 0 {
                        debug!("Successfully acquired Redis connection after {} retries", retry_count);
                    }
                    return Ok(conn);
                },
                Err(e) => {
                    retry_count += 1;

                    if retry_count >= max_retries {
                        // Use structured logging with error details
                        error!(
                            error = %e,
                            retries = max_retries,
                            "Failed to get Redis connection after multiple attempts"
                        );
                        return Err(RedisError::from(io::Error::new(
                            io::ErrorKind::Other,
                            format!("Failed to get connection: {}", e)
                        )));
                    }

                    // Adaptive exponential backoff: 2ms, 6ms, 14ms
                    // This is faster than the previous 5ms, 10ms, 20ms
                    let delay = Duration::from_millis(base_delay_ms * (1 << retry_count));

                    // Log at appropriate level based on retry count
                    if retry_count == 1 {
                        debug!("Retrying Redis connection in {:?} (attempt {}/{})", delay, retry_count, max_retries);
                    } else {
                        warn!("Retrying Redis connection in {:?} (attempt {}/{})", delay, retry_count, max_retries);
                    }

                    tokio::time::sleep(delay).await;
                }
            }
        }
    }

    // Set a key-value pair in Redis (async)
    pub async fn set(&self, key: &str, value: &str) -> RedisResult<()> {
        let mut conn = self.get_connection().await?;
        let _: () = conn.set(key, value).await?;
        Ok(())
    }

    // Set a key-value pair with expiration (async)
    pub async fn setex(&self, key: &str, value: &str, ttl: u64) -> RedisResult<()> {
        let mut conn = self.get_connection().await?;
        let _: () = conn.set_ex(key, value, ttl).await?;
        Ok(())
    }

    // Set expiration for a key (async)
    pub async fn set_expiry(&self, key: &str, ttl: i64) -> RedisResult<()> {
        if ttl <= 0 {
            return Err(RedisError::from(io::Error::new(
                io::ErrorKind::InvalidInput,
                "TTL must be greater than 0",
            )));
        }
        let mut conn = self.get_connection().await?;
        let _: () = conn.expire(key, ttl).await?;
        Ok(())
    }

    // Get a value by key from Redis (async)
    pub async fn get(&self, key: &str) -> RedisResult<String> {
        let mut conn = self.get_connection().await?;
        conn.get(key).await
    }

    // Delete a key from Redis (async)
    pub async fn del(&self, key: &str) -> RedisResult<()> {
        let mut conn = self.get_connection().await?;
        let _: () = conn.del(key).await?;
        Ok(())
    }

    // Increment a key by a value (async)
    pub async fn incr(&self, key: &str, value: i64) -> RedisResult<i64> {
        let mut conn = self.get_connection().await?;
        conn.incr(key, value).await
    }

    // Increment a key by a float value (async)
    pub async fn incrbyfloat(&self, key: &str, value: f64) -> RedisResult<f64> {
        let mut conn = self.get_connection().await?;
        let result: f64 = cmd("INCRBYFLOAT")
            .arg(key)
            .arg(value)
            .query_async(&mut *conn)
            .await?;

        Ok(result)
    }

    // Decrement a key by a value (async)
    pub async fn decr(&self, key: &str, value: i64) -> RedisResult<i64> {
        let mut conn = self.get_connection().await?;
        conn.decr(key, value).await
    }

    // Set a hash field (async)
    pub async fn hset(&self, key: &str, field: &str, value: &str) -> RedisResult<()> {
        let mut conn = self.get_connection().await?;
        let _: () = conn.hset(key, field, value).await?;
        Ok(())
    }

    // Get a hash field (async)
    pub async fn hget(&self, key: &str, field: &str) -> RedisResult<String> {
        let mut conn = self.get_connection().await?;
        conn.hget(key, field).await
    }

    // Get all fields and values of a hash (async)
    pub async fn hgetall(&self, key: &str) -> RedisResult<HashMap<String, String>> {
        let mut conn = self.get_connection().await?;
        conn.hgetall(key).await
    }

    // Increment a hash field by a value (async)
    pub async fn hincr(&self, key: &str, field: &str, value: i64) -> RedisResult<i64> {
        let mut conn = self.get_connection().await?;
        conn.hincr(key, field, value).await
    }

    // Increment a hash field by a float value (async)
    pub async fn hincrbyfloat(&self, key: &str, field: &str, value: f64) -> RedisResult<f64> {
        let mut conn = self.get_connection().await?;
        let result: f64 = cmd("HINCRBYFLOAT")
            .arg(key)
            .arg(field)
            .arg(value)
            .query_async(&mut *conn)
            .await?;

        Ok(result)
    }

    // Decrement a hash field by a value (async)
    pub async fn hdecrby(&self, key: &str, field: &str, value: i64) -> RedisResult<i64> {
        self.hincr(key, field, -value).await
    }

    // Test Redis connection with a PING command (async)
    pub async fn ping(&self) -> RedisResult<()> {
        let mut conn = self.get_connection().await?;
        cmd("PING").query_async(&mut *conn).await
    }

    // Set multiple hash fields and values (HMSET) (async)
    pub async fn hmset(&self, key: &str, fields: &[(String, String)]) -> RedisResult<()> {
        if fields.is_empty() {
            return Ok(());
        }

        let mut conn = self.get_connection().await?;
        let mut cmd = cmd("HMSET");
        cmd.arg(key);

        for (field, value) in fields {
            cmd.arg(field).arg(value);
        }

        let _: () = cmd.query_async(&mut *conn).await?;
        Ok(())
    }

    // Set key if not exists (SETNX) (async)
    pub async fn setnx(&self, key: &str, value: &str) -> RedisResult<bool> {
        let mut conn = self.get_connection().await?;
        let result: i32 = cmd("SETNX").arg(key).arg(value).query_async(&mut *conn).await?;
        Ok(result == 1)
    }

    // Check if key exists (EXISTS) (async)
    pub async fn exists(&self, key: &str) -> RedisResult<bool> {
        let mut conn = self.get_connection().await?;
        let result: i32 = cmd("EXISTS").arg(key).query_async(&mut *conn).await?;
        Ok(result == 1)
    }

    // Check if hash field exists (HEXISTS) (async)
    pub async fn hexists(&self, key: &str, field: &str) -> RedisResult<bool> {
        let mut conn = self.get_connection().await?;
        let result: i32 = cmd("HEXISTS").arg(key).arg(field).query_async(&mut *conn).await?;
        Ok(result == 1)
    }

    // Load Lua script and return SHA (SCRIPT LOAD) (async)
    pub async fn load_script(&self, script: &str) -> RedisResult<String> {
        let mut conn = self.get_connection().await?;
        cmd("SCRIPT").arg("LOAD").arg(script).query_async(&mut *conn).await
    }

    // Execute Lua script by SHA (EVALSHA) (async)
    pub async fn evalsha(&self, sha: &str, keys: &[String], args: &[String]) -> RedisResult<String> {
        let mut conn = self.get_connection().await?;
        let mut cmd = cmd("EVALSHA");
        cmd.arg(sha).arg(keys.len());

        for key in keys {
            cmd.arg(key);
        }

        for arg in args {
            cmd.arg(arg);
        }

        // Use deadpool_redis::redis::Value to handle different return types from Lua scripts
        let result: deadpool_redis::redis::Value = cmd.query_async(&mut *conn).await?;

        // Convert the result to a string representation
        match result {
            deadpool_redis::redis::Value::Nil => Ok("".to_string()), // Return empty string for nil
            deadpool_redis::redis::Value::BulkString(bytes) => {
                String::from_utf8(bytes).map_err(|e| {
                    RedisError::from(io::Error::new(
                        io::ErrorKind::InvalidData,
                        format!("Invalid UTF-8 in script result: {}", e)
                    ))
                })
            },
            deadpool_redis::redis::Value::Okay => Ok("OK".to_string()),
            deadpool_redis::redis::Value::SimpleString(s) => Ok(s),
            deadpool_redis::redis::Value::Int(i) => Ok(i.to_string()),
            deadpool_redis::redis::Value::Array(arr) => {
                // Convert array to JSON-like string representation
                let string_values: Result<Vec<String>, RedisError> = arr.into_iter().map(|v| {
                    match v {
                        deadpool_redis::redis::Value::Nil => Ok("null".to_string()),
                        deadpool_redis::redis::Value::BulkString(bytes) => String::from_utf8(bytes).map_err(|e| {
                            RedisError::from(io::Error::new(
                                io::ErrorKind::InvalidData,
                                format!("Invalid UTF-8 in array element: {}", e)
                            ))
                        }),
                        deadpool_redis::redis::Value::SimpleString(s) => Ok(s),
                        deadpool_redis::redis::Value::Int(i) => Ok(i.to_string()),
                        deadpool_redis::redis::Value::Okay => Ok("OK".to_string()),
                        deadpool_redis::redis::Value::Array(_) => Ok("[nested_array]".to_string()), // Simplified for nested arrays
                        _ => Ok("[unsupported_type]".to_string()), // Handle all other types
                    }
                }).collect();

                match string_values {
                    Ok(values) => Ok(format!("[{}]", values.join(","))),
                    Err(e) => Err(e),
                }
            },
            _ => Ok("[unsupported_type]".to_string()), // Handle all other types like Map, Set, etc.
        }
    }

    // Get the underlying connection pool for health checks
    pub fn get_pool(&self) -> &Pool {
        &self.async_pool
    }

    /// Batch set multiple key-value pairs in Redis (async) - optimized for high throughput
    ///
    /// Uses adaptive chunking and parallel processing for maximum performance
    pub async fn batch_set(&self, pairs: &[(String, String)]) -> RedisResult<()> {
        if pairs.is_empty() {
            return Ok(());
        }

        // Increased chunk size for better throughput
        const CHUNK_SIZE: usize = 5000; // Increased from 2000 to 5000 for better batching

        if pairs.len() <= CHUNK_SIZE {
            // Fast path for smaller batches - avoid overhead of spawning tasks
            let mut conn = self.get_connection().await?;
            let mut pipe = pipe();
            pipe.atomic(); // Ensure the entire batch is atomic

            // Pre-size the pipeline commands
            for (key, value) in pairs {
                pipe.cmd("SET").arg(key).arg(value);
            }

            // Execute pipeline and return result directly
            return pipe.query_async(&mut *conn).await;
        } else {
            // For larger batches, process in chunks with multiple pipelines in parallel
            use tokio::sync::Semaphore;

            // Calculate optimal concurrency based on pool size
            // Use at most 1/4 of the pool for batch operations to leave room for other operations
            let max_concurrent = (self.pool_size / 4).max(16).min(32);

            // Create a semaphore to limit concurrent Redis operations
            // This provides backpressure and prevents connection exhaustion
            let semaphore = Arc::new(Semaphore::new(max_concurrent));

            // Calculate optimal chunk size based on total items
            // For very large batches, use larger chunks to reduce overhead
            let adaptive_chunk_size = if pairs.len() > 50000 {
                10000
            } else if pairs.len() > 20000 {
                7500
            } else {
                CHUNK_SIZE
            };

            // Split data into chunks
            let chunks: Vec<&[(String, String)]> = pairs.chunks(adaptive_chunk_size).collect();

            // Pre-allocate futures vector with exact capacity
            let mut futures: Vec<tokio::task::JoinHandle<Result<(), RedisError>>> = Vec::with_capacity(chunks.len());

            // Process each chunk in parallel
            for chunk in chunks {
                let chunk_owned = chunk.to_vec(); // Need to clone for async
                let self_clone = self.clone();
                let semaphore_clone = semaphore.clone();

                futures.push(tokio::spawn(async move {
                    // Acquire permit from semaphore (will wait if max concurrent operations reached)
                    let _permit = semaphore_clone.acquire().await.unwrap();

                    // Get connection and prepare pipeline
                    let mut conn = self_clone.get_connection().await?;
                    let mut pipe = pipe();
                    pipe.atomic();

                    // Add all commands to pipeline
                    for (key, value) in &chunk_owned {
                        pipe.cmd("SET").arg(key).arg(value);
                    }

                    // Execute pipeline
                    let result: () = pipe.query_async(&mut *conn).await?;
                    Ok(result)
                }));
            }

            // Process all futures in parallel with improved error handling
            let results = futures::future::join_all(futures).await;

            // Check results with early return on first error
            for (i, result) in results.into_iter().enumerate() {
                match result {
                    Ok(redis_result) => {
                        if let Err(e) = redis_result {
                            error!("Redis error in batch chunk {}: {}", i, e);
                            return Err(e);
                        }
                    },
                    Err(e) => {
                        error!("Task join error in batch chunk {}: {}", i, e);
                        return Err(RedisError::from(io::Error::new(
                            io::ErrorKind::Other,
                            format!("Task join error in chunk {}: {}", i, e)
                        )));
                    }
                }
            }

            Ok(())
        }
    }

    /// Batch set multiple key-value pairs with expiration in Redis (async) - optimized for high throughput
    ///
    /// Uses adaptive chunking and parallel processing for maximum performance
    pub async fn batch_setex(&self, pairs: &[(String, String, u64)]) -> RedisResult<()> {
        if pairs.is_empty() {
            return Ok(());
        }

        // Increased chunk size for better throughput
        const CHUNK_SIZE: usize = 5000; // Increased from 2000 to 5000 for better batching

        if pairs.len() <= CHUNK_SIZE {
            // Fast path for smaller batches - avoid overhead of spawning tasks
            let mut conn = self.get_connection().await?;
            let mut pipe = pipe();
            pipe.atomic(); // Ensure the entire batch is atomic

            for (key, value, ttl) in pairs {
                pipe.cmd("SETEX").arg(key).arg(*ttl).arg(value);
            }

            // Execute pipeline and return result directly
            return pipe.query_async(&mut *conn).await;
        } else {
            // For larger batches, process in chunks with multiple pipelines in parallel
            use tokio::sync::Semaphore;

            // Calculate optimal concurrency based on pool size
            // Use at most 1/4 of the pool for batch operations to leave room for other operations
            let max_concurrent = (self.pool_size / 4).max(16).min(32);

            // Create a semaphore to limit concurrent Redis operations
            // This provides backpressure and prevents connection exhaustion
            let semaphore = Arc::new(Semaphore::new(max_concurrent));

            // Calculate optimal chunk size based on total items
            // For very large batches, use larger chunks to reduce overhead
            let adaptive_chunk_size = if pairs.len() > 50000 {
                10000
            } else if pairs.len() > 20000 {
                7500
            } else {
                CHUNK_SIZE
            };

            // Split data into chunks
            let chunks: Vec<&[(String, String, u64)]> = pairs.chunks(adaptive_chunk_size).collect();

            // Pre-allocate futures vector with exact capacity
            let mut futures: Vec<tokio::task::JoinHandle<Result<(), RedisError>>> = Vec::with_capacity(chunks.len());

            // Process each chunk in parallel
            for chunk in chunks {
                let chunk_owned = chunk.to_vec(); // Need to clone for async
                let self_clone = self.clone();
                let semaphore_clone = semaphore.clone();

                futures.push(tokio::spawn(async move {
                    // Acquire permit from semaphore (will wait if max concurrent operations reached)
                    let _permit = semaphore_clone.acquire().await.unwrap();

                    // Get connection and prepare pipeline
                    let mut conn = self_clone.get_connection().await?;
                    let mut pipe = pipe();
                    pipe.atomic();

                    // Add all commands to pipeline
                    for (key, value, ttl) in &chunk_owned {
                        pipe.cmd("SETEX").arg(key).arg(*ttl).arg(value);
                    }

                    // Execute pipeline
                    let result: () = pipe.query_async(&mut *conn).await?;
                    Ok(result)
                }));
            }

            // Process all futures in parallel with improved error handling
            let results = futures::future::join_all(futures).await;

            // Check results with early return on first error
            for (i, result) in results.into_iter().enumerate() {
                match result {
                    Ok(redis_result) => {
                        if let Err(e) = redis_result {
                            error!("Redis error in batch chunk {}: {}", i, e);
                            return Err(e);
                        }
                    },
                    Err(e) => {
                        error!("Task join error in batch chunk {}: {}", i, e);
                        return Err(RedisError::from(io::Error::new(
                            io::ErrorKind::Other,
                            format!("Task join error in chunk {}: {}", i, e)
                        )));
                    }
                }
            }

            Ok(())
        }
    }

    /// Delete multiple keys from Redis (async)
    pub async fn del_multiple(&self, keys: &[String]) -> RedisResult<i32> {
        if keys.is_empty() {
            return Ok(0);
        }

        let mut conn = self.get_connection().await?;
        let deleted_count: i32 = conn.del(keys).await?;
        Ok(deleted_count)
    }

    /// Add members to a set (async)
    pub async fn sadd(&self, key: &str, members: &[String]) -> RedisResult<i32> {
        if members.is_empty() {
            return Ok(0);
        }

        let mut conn = self.get_connection().await?;
        let mut cmd = cmd("SADD");
        cmd.arg(key);
        for member in members {
            cmd.arg(member);
        }
        let added_count: i32 = cmd.query_async(&mut *conn).await?;
        Ok(added_count)
    }

    /// Get all members of a set (async)
    pub async fn smembers(&self, key: &str) -> RedisResult<Vec<String>> {
        let mut conn = self.get_connection().await?;
        let members: Vec<String> = conn.smembers(key).await?;
        Ok(members)
    }

    /// Delete hash fields (async)
    pub async fn hdel(&self, key: &str, fields: &[String]) -> RedisResult<i32> {
        if fields.is_empty() {
            return Ok(0);
        }

        let mut conn = self.get_connection().await?;
        let mut cmd = cmd("HDEL");
        cmd.arg(key);
        for field in fields {
            cmd.arg(field);
        }
        let deleted_count: i32 = cmd.query_async(&mut *conn).await?;
        Ok(deleted_count)
    }

    /// Scan hash fields and values (async)
    pub async fn hscan(&self, key: &str, cursor: u64, pattern: Option<&str>, count: Option<u32>) -> RedisResult<(u64, Vec<(String, String)>)> {
        let mut conn = self.get_connection().await?;
        let mut cmd = cmd("HSCAN");
        cmd.arg(key).arg(cursor);

        if let Some(pattern) = pattern {
            cmd.arg("MATCH").arg(pattern);
        }

        if let Some(count) = count {
            cmd.arg("COUNT").arg(count);
        }

        let result: (u64, Vec<String>) = cmd.query_async(&mut *conn).await?;
        let (next_cursor, field_values) = result;

        // Convert flat Vec<String> to Vec<(String, String)> pairs
        let mut pairs = Vec::new();
        for chunk in field_values.chunks(2) {
            if chunk.len() == 2 {
                pairs.push((chunk[0].clone(), chunk[1].clone()));
            }
        }

        Ok((next_cursor, pairs))
    }

    /// Get number of fields in a hash (async)
    pub async fn hlen(&self, key: &str) -> RedisResult<i32> {
        let mut conn = self.get_connection().await?;
        let length: i32 = conn.hlen(key).await?;
        Ok(length)
    }

    /// Batch delete multiple keys from Redis (async) - optimized for high throughput
    ///
    /// Uses adaptive chunking and parallel processing for maximum performance
    pub async fn batch_del(&self, keys: &[String]) -> RedisResult<()> {
        if keys.is_empty() {
            return Ok(());
        }

        // Increased chunk size for better throughput
        const CHUNK_SIZE: usize = 5000; // Increased from 2000 to 5000 for better batching

        if keys.len() <= CHUNK_SIZE {
            // Fast path for smaller batches - avoid overhead of spawning tasks
            let mut conn = self.get_connection().await?;
            let _: () = conn.del(keys).await?;
            return Ok(());
        } else {
            // For larger batches, process in chunks with multiple pipelines in parallel
            use tokio::sync::Semaphore;

            // Calculate optimal concurrency based on pool size
            // Use at most 1/4 of the pool for batch operations to leave room for other operations
            let max_concurrent = (self.pool_size / 4).max(16).min(32);

            // Create a semaphore to limit concurrent Redis operations
            // This provides backpressure and prevents connection exhaustion
            let semaphore = Arc::new(Semaphore::new(max_concurrent));

            // Calculate optimal chunk size based on total items
            // For very large batches, use larger chunks to reduce overhead
            let adaptive_chunk_size = if keys.len() > 50000 {
                10000
            } else if keys.len() > 20000 {
                7500
            } else {
                CHUNK_SIZE
            };

            // Split data into chunks
            let chunks: Vec<&[String]> = keys.chunks(adaptive_chunk_size).collect();

            // Pre-allocate futures vector with exact capacity
            let mut futures: Vec<tokio::task::JoinHandle<Result<(), RedisError>>> = Vec::with_capacity(chunks.len());

            // Process each chunk in parallel
            for chunk in chunks {
                let chunk_owned = chunk.to_vec(); // Need to clone for async
                let self_clone = self.clone();
                let semaphore_clone = semaphore.clone();

                futures.push(tokio::spawn(async move {
                    // Acquire permit from semaphore (will wait if max concurrent operations reached)
                    let _permit = semaphore_clone.acquire().await.unwrap();

                    // Get connection and execute DEL command
                    // For DEL, we don't need a pipeline since it's a single command
                    let mut conn = self_clone.get_connection().await?;
                    let _: () = conn.del(&chunk_owned).await?;
                    Ok(())
                }));
            }

            // Process all futures in parallel with improved error handling
            let results = futures::future::join_all(futures).await;

            // Check results with early return on first error
            for (i, result) in results.into_iter().enumerate() {
                match result {
                    Ok(redis_result) => {
                        if let Err(e) = redis_result {
                            error!("Redis error in batch chunk {}: {}", i, e);
                            return Err(e);
                        }
                    },
                    Err(e) => {
                        error!("Task join error in batch chunk {}: {}", i, e);
                        return Err(RedisError::from(io::Error::new(
                            io::ErrorKind::Other,
                            format!("Task join error in chunk {}: {}", i, e)
                        )));
                    }
                }
            }

            Ok(())
        }
    }

    /// Batch get multiple values from Redis (async) - optimized for high throughput
    ///
    /// Uses chunking for large key sets to avoid overwhelming Redis
    pub async fn batch_get(&self, keys: &[String]) -> RedisResult<Vec<Option<String>>> {
        if keys.is_empty() {
            return Ok(vec![]);
        }

        // For large key sets, we need to chunk to avoid overwhelming Redis
        const CHUNK_SIZE: usize = 5000;

        if keys.len() <= CHUNK_SIZE {
            // Fast path for smaller batches
            let mut conn = self.get_connection().await?;
            return conn.get(keys).await;
        } else {
            // For larger batches, process in chunks and combine results
            use tokio::sync::Semaphore;

            // Calculate optimal concurrency based on pool size
            let max_concurrent = (self.pool_size / 4).max(16).min(32);

            // Create a semaphore to limit concurrent Redis operations
            let semaphore = Arc::new(Semaphore::new(max_concurrent));

            // Calculate optimal chunk size based on total items
            let adaptive_chunk_size = if keys.len() > 50000 {
                10000
            } else if keys.len() > 20000 {
                7500
            } else {
                CHUNK_SIZE
            };

            // Split data into chunks
            let chunks: Vec<&[String]> = keys.chunks(adaptive_chunk_size).collect();

            // Pre-allocate futures vector with exact capacity
            let mut futures: Vec<tokio::task::JoinHandle<Result<Vec<Option<String>>, RedisError>>> = Vec::with_capacity(chunks.len());

            // Process each chunk in parallel
            for chunk in chunks {
                let chunk_owned = chunk.to_vec(); // Need to clone for async
                let self_clone = self.clone();
                let semaphore_clone = semaphore.clone();

                futures.push(tokio::spawn(async move {
                    // Acquire permit from semaphore
                    let _permit = semaphore_clone.acquire().await.unwrap();

                    // Get connection and execute GET command
                    let mut conn = self_clone.get_connection().await?;
                    conn.get(&chunk_owned).await
                }));
            }

            // Process all futures in parallel
            let results = futures::future::join_all(futures).await;

            // Combine results from all chunks
            let mut combined_results = Vec::with_capacity(keys.len());

            for (i, result) in results.into_iter().enumerate() {
                match result {
                    Ok(chunk_result) => {
                        match chunk_result {
                            Ok(values) => combined_results.extend(values),
                            Err(e) => {
                                error!("Redis error in batch_get chunk {}: {}", i, e);
                                return Err(e);
                            }
                        }
                    },
                    Err(e) => {
                        error!("Task join error in batch_get chunk {}: {}", i, e);
                        return Err(RedisError::from(io::Error::new(
                            io::ErrorKind::Other,
                            format!("Task join error in chunk {}: {}", i, e)
                        )));
                    }
                }
            }

            Ok(combined_results)
        }
    }
}
