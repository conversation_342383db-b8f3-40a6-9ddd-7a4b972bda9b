use dashmap::DashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::interval;
use tokio::sync::RwLock;
use tracing::{debug, info};

/// Token bucket for rate limiting using atomic operations for lock-free performance
#[derive(Debug)]
pub struct TokenBucket {
    tokens: AtomicU64,
    last_refill: AtomicU64,
    capacity: u64,
    refill_rate: u64, // tokens per second
}

impl TokenBucket {
    pub fn new(capacity: u64, refill_rate: u64) -> Self {
        let now = Instant::now().elapsed().as_millis() as u64;
        Self {
            tokens: AtomicU64::new(capacity),
            last_refill: AtomicU64::new(now),
            capacity,
            refill_rate,
        }
    }

    /// Try to consume tokens from the bucket
    /// Returns true if tokens were available, false if rate limited
    pub fn try_consume(&self, tokens_needed: u64) -> bool {
        let now = Instant::now().elapsed().as_millis() as u64;
        
        // Refill tokens based on elapsed time
        self.refill_tokens(now);
        
        // Try to consume tokens atomically
        loop {
            let current_tokens = self.tokens.load(Ordering::Acquire);
            
            if current_tokens < tokens_needed {
                return false; // Rate limited
            }
            
            let new_tokens = current_tokens - tokens_needed;
            
            // Use compare_exchange to ensure atomicity
            match self.tokens.compare_exchange_weak(
                current_tokens,
                new_tokens,
                Ordering::Release,
                Ordering::Relaxed,
            ) {
                Ok(_) => return true, // Successfully consumed tokens
                Err(_) => continue,   // Retry due to concurrent modification
            }
        }
    }

    fn refill_tokens(&self, now: u64) {
        let last_refill = self.last_refill.load(Ordering::Acquire);
        let elapsed_ms = now.saturating_sub(last_refill);
        
        if elapsed_ms < 100 {
            return; // Don't refill too frequently (every 100ms minimum)
        }
        
        // Calculate tokens to add based on elapsed time
        let tokens_to_add = (elapsed_ms * self.refill_rate) / 1000;
        
        if tokens_to_add == 0 {
            return;
        }
        
        // Update last refill time first
        if self.last_refill.compare_exchange_weak(
            last_refill,
            now,
            Ordering::Release,
            Ordering::Relaxed,
        ).is_err() {
            return; // Another thread is handling refill
        }
        
        // Add tokens up to capacity
        loop {
            let current_tokens = self.tokens.load(Ordering::Acquire);
            let new_tokens = (current_tokens + tokens_to_add).min(self.capacity);
            
            if new_tokens == current_tokens {
                break; // Already at capacity
            }
            
            if self.tokens.compare_exchange_weak(
                current_tokens,
                new_tokens,
                Ordering::Release,
                Ordering::Relaxed,
            ).is_ok() {
                break; // Successfully updated
            }
        }
    }
}

impl Clone for TokenBucket {
    fn clone(&self) -> Self {
        let current_tokens = self.tokens.load(Ordering::Acquire);
        let last_refill = self.last_refill.load(Ordering::Acquire);

        Self {
            tokens: AtomicU64::new(current_tokens),
            last_refill: AtomicU64::new(last_refill),
            capacity: self.capacity,
            refill_rate: self.refill_rate,
        }
    }
}

/// Client rate limiter entry with automatic cleanup
#[derive(Debug)]
struct ClientRateLimit {
    bucket: TokenBucket,
    last_access: AtomicU64,
}

impl ClientRateLimit {
    fn new(capacity: u64, refill_rate: u64) -> Self {
        let now = Instant::now().elapsed().as_millis() as u64;
        Self {
            bucket: TokenBucket::new(capacity, refill_rate),
            last_access: AtomicU64::new(now),
        }
    }

    fn try_consume(&self, tokens_needed: u64) -> bool {
        let now = Instant::now().elapsed().as_millis() as u64;
        self.last_access.store(now, Ordering::Release);
        self.bucket.try_consume(tokens_needed)
    }

    fn is_expired(&self, now: u64, timeout_ms: u64) -> bool {
        let last_access = self.last_access.load(Ordering::Acquire);
        now.saturating_sub(last_access) > timeout_ms
    }
}

impl Clone for ClientRateLimit {
    fn clone(&self) -> Self {
        let last_access = self.last_access.load(Ordering::Acquire);

        Self {
            bucket: self.bucket.clone(),
            last_access: AtomicU64::new(last_access),
        }
    }
}

/// Rate limiting type configuration
#[derive(Debug, Clone, PartialEq)]
pub enum RateLimitType {
    Global,
    PerClient,
    Both,
}

impl RateLimitType {
    pub fn from_string(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "global" => Some(RateLimitType::Global),
            "per_client" => Some(RateLimitType::PerClient),
            "both" => Some(RateLimitType::Both),
            _ => None,
        }
    }
}

/// High-performance rate limiter with global and per-client tracking
pub struct RateLimiter {
    enabled: bool,
    rate_limit_type: RateLimitType,
    // Per-client rate limiting
    clients: Arc<DashMap<String, ClientRateLimit>>,
    default_capacity: u64,
    default_refill_rate: u64,
    // Global rate limiting
    global_bucket: Option<TokenBucket>,
    // Common settings
    cleanup_interval: Duration,
    client_timeout: Duration,
}

impl RateLimiter {
    pub fn new(
        enabled: bool,
        rate_limit_type: &str,
        requests_per_second: u32,
        burst_size: u32,
        global_requests_per_second: u32,
        global_burst_size: u32,
        cleanup_interval_secs: u64,
    ) -> Self {
        let cleanup_interval = Duration::from_secs(cleanup_interval_secs);
        let client_timeout = Duration::from_secs(cleanup_interval_secs * 2); // 2x cleanup interval

        let rate_limit_type = RateLimitType::from_string(rate_limit_type)
            .unwrap_or(RateLimitType::PerClient);

        // Create global bucket if needed
        let global_bucket = if rate_limit_type == RateLimitType::Global || rate_limit_type == RateLimitType::Both {
            Some(TokenBucket::new(global_burst_size as u64, global_requests_per_second as u64))
        } else {
            None
        };

        Self {
            enabled,
            rate_limit_type,
            clients: Arc::new(DashMap::new()),
            default_capacity: burst_size as u64,
            default_refill_rate: requests_per_second as u64,
            global_bucket,
            cleanup_interval,
            client_timeout,
        }
    }

    /// Check if rate limiting is enabled (zero overhead when disabled)
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    /// Try to consume a token for the given client
    /// Returns true if allowed, false if rate limited
    /// Applies global and/or per-client rate limiting based on configuration
    pub fn try_consume(&self, client_id: &str) -> bool {
        if !self.enabled {
            return true; // Fast path: rate limiting disabled
        }

        match self.rate_limit_type {
            RateLimitType::Global => {
                // Only check global rate limit
                if let Some(ref global_bucket) = self.global_bucket {
                    global_bucket.try_consume(1)
                } else {
                    true // No global bucket configured, allow request
                }
            },
            RateLimitType::PerClient => {
                // Only check per-client rate limit
                self.check_per_client_limit(client_id)
            },
            RateLimitType::Both => {
                // Check both global and per-client limits
                // Both must pass for the request to be allowed
                let global_allowed = if let Some(ref global_bucket) = self.global_bucket {
                    global_bucket.try_consume(1)
                } else {
                    true // No global bucket configured, allow request
                };

                if !global_allowed {
                    return false; // Global limit exceeded
                }

                // Global limit passed, now check per-client limit
                self.check_per_client_limit(client_id)
            }
        }
    }

    /// Check per-client rate limit for the given client
    fn check_per_client_limit(&self, client_id: &str) -> bool {
        // Get or create client rate limiter
        let client_limiter = self.clients
            .entry(client_id.to_string())
            .or_insert_with(|| {
                debug!("Creating new rate limiter for client: {}", client_id);
                ClientRateLimit::new(self.default_capacity, self.default_refill_rate)
            });

        client_limiter.try_consume(1)
    }

    /// Start the cleanup task to remove expired client entries
    pub fn start_cleanup_task(self: Arc<Self>) {
        if !self.enabled {
            return; // No cleanup needed when disabled
        }

        let rate_limiter = Arc::clone(&self);
        tokio::spawn(async move {
            let mut cleanup_interval = interval(rate_limiter.cleanup_interval);
            
            info!("Rate limiter cleanup task started (interval: {:?})", rate_limiter.cleanup_interval);
            
            loop {
                cleanup_interval.tick().await;
                rate_limiter.cleanup_expired_clients().await;
            }
        });
    }

    async fn cleanup_expired_clients(&self) {
        let now = Instant::now().elapsed().as_millis() as u64;
        let timeout_ms = self.client_timeout.as_millis() as u64;
        let mut removed_count = 0;

        // Collect expired client IDs
        let expired_clients: Vec<String> = self.clients
            .iter()
            .filter_map(|entry| {
                if entry.value().is_expired(now, timeout_ms) {
                    Some(entry.key().clone())
                } else {
                    None
                }
            })
            .collect();

        // Remove expired clients
        for client_id in expired_clients {
            if self.clients.remove(&client_id).is_some() {
                removed_count += 1;
                debug!("Removed expired rate limiter for client: {}", client_id);
            }
        }

        if removed_count > 0 {
            info!("Rate limiter cleanup: removed {} expired clients, active clients: {}", 
                  removed_count, self.clients.len());
        }
    }

    /// Get current statistics for monitoring
    pub fn get_stats(&self) -> RateLimiterStats {
        let (global_capacity, global_refill_rate) = if let Some(ref global_bucket) = self.global_bucket {
            (Some(global_bucket.capacity), Some(global_bucket.refill_rate))
        } else {
            (None, None)
        };

        RateLimiterStats {
            enabled: self.enabled,
            rate_limit_type: self.rate_limit_type.clone(),
            active_clients: self.clients.len(),
            per_client_capacity: self.default_capacity,
            per_client_refill_rate: self.default_refill_rate,
            global_capacity,
            global_refill_rate,
        }
    }
}

impl Clone for RateLimiter {
    fn clone(&self) -> Self {
        Self {
            enabled: self.enabled,
            rate_limit_type: self.rate_limit_type.clone(),
            clients: Arc::new(DashMap::new()), // Start with empty clients for the clone
            default_capacity: self.default_capacity,
            default_refill_rate: self.default_refill_rate,
            global_bucket: self.global_bucket.clone(),
            cleanup_interval: self.cleanup_interval,
            client_timeout: self.client_timeout,
        }
    }
}

/// Hot-reloadable rate limiter wrapper
pub struct HotReloadableRateLimiter {
    inner: Arc<RwLock<RateLimiter>>,
}

impl HotReloadableRateLimiter {
    pub fn new(rate_limiter: RateLimiter) -> Self {
        Self {
            inner: Arc::new(RwLock::new(rate_limiter)),
        }
    }

    /// Try to consume a token (delegates to inner rate limiter)
    pub async fn try_consume(&self, client_id: &str) -> bool {
        let limiter = self.inner.read().await;
        limiter.try_consume(client_id)
    }

    /// Check if rate limiting is enabled
    pub async fn is_enabled(&self) -> bool {
        let limiter = self.inner.read().await;
        limiter.is_enabled()
    }

    /// Get current statistics
    pub async fn get_stats(&self) -> RateLimiterStats {
        let limiter = self.inner.read().await;
        limiter.get_stats()
    }

    /// Update rate limiting configuration at runtime
    pub async fn update_config(
        &self,
        enabled: bool,
        rate_limit_type: &str,
        requests_per_second: u32,
        burst_size: u32,
        global_requests_per_second: u32,
        global_burst_size: u32,
        cleanup_interval_secs: u64,
    ) -> Result<(), String> {
        info!("Updating rate limiter configuration");

        // Create a new rate limiter with updated configuration
        let new_limiter = RateLimiter::new(
            enabled,
            rate_limit_type,
            requests_per_second,
            burst_size,
            global_requests_per_second,
            global_burst_size,
            cleanup_interval_secs,
        );

        // Replace the inner rate limiter
        let mut limiter = self.inner.write().await;
        *limiter = new_limiter;

        info!("Rate limiter configuration updated successfully");
        Ok(())
    }

    /// Start cleanup task (delegates to inner rate limiter)
    pub async fn start_cleanup_task(&self) {
        let limiter = self.inner.read().await;
        let limiter_clone = Arc::new(limiter.clone());
        drop(limiter);
        limiter_clone.start_cleanup_task();
    }
}

impl Clone for HotReloadableRateLimiter {
    fn clone(&self) -> Self {
        Self {
            inner: self.inner.clone(),
        }
    }
}

/// Statistics for monitoring rate limiter performance
#[derive(Debug, Clone)]
pub struct RateLimiterStats {
    pub enabled: bool,
    pub rate_limit_type: RateLimitType,
    pub active_clients: usize,
    pub per_client_capacity: u64,
    pub per_client_refill_rate: u64,
    pub global_capacity: Option<u64>,
    pub global_refill_rate: Option<u64>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_bucket_basic() {
        let bucket = TokenBucket::new(10, 5); // 10 capacity, 5 tokens/sec
        
        // Should be able to consume up to capacity
        assert!(bucket.try_consume(5));
        assert!(bucket.try_consume(5));
        
        // Should be rate limited now
        assert!(!bucket.try_consume(1));
    }

    #[tokio::test]
    async fn test_rate_limiter_disabled() {
        let limiter = Arc::new(RateLimiter::new(false, "per_client", 10, 20, 100, 200, 60));

        // Should always allow when disabled
        for _ in 0..100 {
            assert!(limiter.try_consume("test_client"));
        }
    }

    #[tokio::test]
    async fn test_rate_limiter_per_client() {
        let limiter = Arc::new(RateLimiter::new(true, "per_client", 10, 5, 100, 200, 60));

        // Should allow up to burst size
        for _ in 0..5 {
            assert!(limiter.try_consume("test_client"));
        }

        // Should be rate limited now
        assert!(!limiter.try_consume("test_client"));
    }

    #[tokio::test]
    async fn test_rate_limiter_per_client_independent() {
        let limiter = Arc::new(RateLimiter::new(true, "per_client", 10, 2, 100, 200, 60));

        // Each client should have independent limits
        assert!(limiter.try_consume("client1"));
        assert!(limiter.try_consume("client1"));
        assert!(!limiter.try_consume("client1")); // Rate limited

        assert!(limiter.try_consume("client2"));
        assert!(limiter.try_consume("client2"));
        assert!(!limiter.try_consume("client2")); // Rate limited
    }

    #[tokio::test]
    async fn test_rate_limiter_global() {
        let limiter = Arc::new(RateLimiter::new(true, "global", 100, 200, 10, 3, 60));

        // Should allow up to global burst size across all clients
        assert!(limiter.try_consume("client1"));
        assert!(limiter.try_consume("client2"));
        assert!(limiter.try_consume("client3"));

        // Should be globally rate limited now
        assert!(!limiter.try_consume("client1"));
        assert!(!limiter.try_consume("client2"));
        assert!(!limiter.try_consume("client4"));
    }

    #[tokio::test]
    async fn test_rate_limiter_both() {
        let limiter = Arc::new(RateLimiter::new(true, "both", 10, 2, 100, 5, 60));

        // Should allow up to per-client burst size for first client
        assert!(limiter.try_consume("client1"));
        assert!(limiter.try_consume("client1"));
        assert!(!limiter.try_consume("client1")); // Per-client rate limited

        // Should allow up to remaining global capacity for second client
        assert!(limiter.try_consume("client2"));
        assert!(limiter.try_consume("client2"));
        assert!(limiter.try_consume("client2")); // Uses last global token

        // Should be globally rate limited now
        assert!(!limiter.try_consume("client3"));
        assert!(!limiter.try_consume("client2")); // Even though client2 has per-client tokens
    }
}
